in prediction.py we have 3 models with different interfaces, we need to create a unified interface for all models.

predict_proba is the unified interface we want to use for all models.

models/train_bkt.py is the only one with predict_proba interface.
models/traing_pfa.py and models/train_dkt.py do not have predict_proba interface. they have predict interface.

Task 1:
create a wrapper class for PFA and DKT models to have a unified interface for all models.   

Error:
- WARNING - DKT prediction error: 'dict' object has no attribute 'predict_proba'
- WARNING - PFA prediction error: 'dict' object has no attribute 'predict_proba'

