#!/usr/bin/env python3
"""
Deep Knowledge Tracing (DKT) Model Implementation
================================================

This module implements a Deep Knowledge Tracing model using TensorFlow.
DKT uses LSTM networks to model student knowledge state over time.

Author: ML Engineer
Date: 2025
"""

import os
import sys
import random
import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LSTM, Dense, Dropout, Embedding, Input, Masking
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
from tensorflow.keras.utils import pad_sequences
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.model_selection import train_test_split
from typing import Dict, List, Tuple, Any, Optional
import logging
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class DeepKnowledgeTracing:
    """
    Deep Knowledge Tracing model using LSTM networks
    
    DKT models student knowledge state as a sequence prediction problem,
    using LSTM to capture temporal dependencies in learning.
    """
    
    def __init__(self, 
                 hidden_dim: int = 32,
                 num_layers: int = 2,
                 dropout: float = 0.2,
                 learning_rate: float = 0.01,
                 batch_size: int = 64,
                 max_epochs: int = 50,
                 patience: int = 10,
                 max_seq_len: int = 200):
        """
        Initialize DKT model
        
        Args:
            hidden_dim: Hidden dimension of LSTM layers
            num_layers: Number of LSTM layers
            dropout: Dropout rate
            learning_rate: Learning rate for optimizer
            batch_size: Batch size for training
            max_epochs: Maximum number of training epochs
            patience: Early stopping patience
            max_seq_len: Maximum sequence length
        """
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.dropout = dropout
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.max_epochs = max_epochs
        self.patience = patience
        self.max_seq_len = max_seq_len
        
        # Model components
        self.model = None
        self.vocab_size = None
        self.n_skills = None
        self.skill_encoders = {}
        self.is_fitted = False
        
        # Training history
        self.history = None
        self.best_val_loss = float('inf')
    
    def _prepare_data(self, df: pd.DataFrame, skill_col: str = 'skill_name', 
                     user_col: str = 'user_id', correct_col: str = 'correct') -> pd.DataFrame:
        """Prepare and clean data for DKT"""
        logger.info("Preparing data for DKT...")
        
        # Required columns
        required_cols = [user_col, skill_col, correct_col]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Clean data
        df_clean = df.dropna(subset=required_cols).copy()
        df_clean[correct_col] = df_clean[correct_col].astype(int)
        
        # Sort by user and order
        if 'order_id' in df_clean.columns:
            df_clean = df_clean.sort_values([user_col, 'order_id'])
        else:
            df_clean = df_clean.sort_values([user_col])
            df_clean['order_id'] = df_clean.groupby(user_col).cumcount() + 1
        
        logger.info(f"Data prepared: {len(df_clean)} interactions")
        return df_clean
    
    def _encode_skills(self, df: pd.DataFrame, skill_col: str = 'skill_name') -> pd.DataFrame:
        """Encode skills to numerical IDs"""
        if not self.skill_encoders:
            unique_skills = df[skill_col].unique()
            self.skill_encoders = {skill: idx for idx, skill in enumerate(unique_skills)}
            self.n_skills = len(unique_skills)
        
        df_encoded = df.copy()
        df_encoded['skill_id'] = df_encoded[skill_col].map(self.skill_encoders)
        df_encoded['skill_id'] = df_encoded['skill_id'].fillna(-1).astype(int)
        
        return df_encoded
    
    def _create_sequences(self, df: pd.DataFrame, user_col: str = 'user_id',
                         skill_col: str = 'skill_id', correct_col: str = 'correct') -> List[Dict]:
        """Create sequences for DKT training"""
        logger.info("Creating sequences for DKT...")
        
        sequences = []
        
        for user_id, user_data in df.groupby(user_col):
            user_data = user_data.sort_values('order_id')
            
            skills = user_data[skill_col].values
            corrects = user_data[correct_col].values
            
            if len(skills) < 2:  # Need at least 2 interactions
                continue
            
            # Create input sequences
            # DKT input: skill_id + (n_skills * previous_correct)
            inputs = []
            targets = []
            
            for i in range(len(skills)):
                if i == 0:
                    # First interaction: just skill
                    input_val = skills[i]
                else:
                    # Subsequent interactions: skill + previous correctness
                    input_val = skills[i] + (self.n_skills * corrects[i-1])
                
                inputs.append(input_val)
                targets.append(corrects[i])
            
            # Remove the first target (no prediction for first interaction)
            inputs = inputs[:-1]  # Remove last input
            targets = targets[1:]  # Remove first target
            
            if len(inputs) > 0:
                sequences.append({
                    'user_id': user_id,
                    'inputs': inputs,
                    'targets': targets,
                    'length': len(inputs)
                })
        
        logger.info(f"Created {len(sequences)} sequences")
        return sequences
    
    def _split_sequences(self, sequences: List[Dict], validation_split: float = 0.2) -> Tuple[List[Dict], List[Dict]]:
        """Split sequences into train and validation sets"""
        # Use random.shuffle instead of np.random.shuffle for lists
        random.shuffle(sequences)
        
        split_idx = int(len(sequences) * (1 - validation_split))
        
        train_sequences = sequences[:split_idx]
        val_sequences = sequences[split_idx:]
        
        logger.info(f"Train sequences: {len(train_sequences)}")
        logger.info(f"Validation sequences: {len(val_sequences)}")
        
        return train_sequences, val_sequences
    
    def _prepare_sequences_for_training(self, sequences: List[Dict]) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Prepare sequences for TensorFlow training"""
        # Extract inputs and targets
        all_inputs = [seq['inputs'] for seq in sequences]
        all_targets = [seq['targets'] for seq in sequences]
        all_lengths = [seq['length'] for seq in sequences]

        # Pad sequences
        X = pad_sequences(all_inputs, maxlen=self.max_seq_len, padding='post', value=0)
        y = pad_sequences(all_targets, maxlen=self.max_seq_len, padding='post', value=0)
        y = np.expand_dims(y, -1)

        # Create sample weights (1 for real data, 0 for padding)
        sample_weights = np.zeros_like(y, dtype=np.float32)
        for i, length in enumerate(all_lengths):
            actual_length = min(length, self.max_seq_len)
            sample_weights[i, :actual_length] = 1.0

        return X, y, sample_weights
    
    def _build_model(self) -> Model:
        """Build the DKT model architecture"""
        logger.info("Building DKT model...")
        
        # Calculate vocabulary size (skills + skills*2 for correct/incorrect)
        self.vocab_size = self.n_skills * 2
        
        # Input layer
        inputs = Input(shape=(self.max_seq_len,), name='skill_inputs')
        
        # Embedding layer
        embedded = Embedding(
            input_dim=self.vocab_size + 1,  # +1 for padding
            output_dim=self.hidden_dim,
            mask_zero=True,
            name='skill_embedding'
        )(inputs)
        
        # LSTM layers
        x = embedded
        for i in range(self.num_layers):
            return_sequences = True  # Return sequences for all layers
            x = LSTM(
                self.hidden_dim,
                return_sequences=return_sequences,
                dropout=self.dropout,
                recurrent_dropout=self.dropout,
                name=f'lstm_{i+1}'
            )(x)

        # Output layer - predict for each timestep
        outputs = Dense(1, activation='sigmoid', name='prediction')(x)
        
        # Create model
        model = Model(inputs=inputs, outputs=outputs, name='DKT')
        
        # Compile model with standard loss
        model.compile(
            optimizer=Adam(learning_rate=self.learning_rate),
            loss='binary_crossentropy',
            metrics=['accuracy']
        )
        
        logger.info("Model built successfully")
        logger.info(f"Model parameters: {model.count_params():,}")
        
        return model
    
    def fit(self, df: pd.DataFrame, skill_col: str = 'skill_name', user_col: str = 'user_id',
            correct_col: str = 'correct', validation_split: float = 0.2):
        """Train the DKT model"""
        logger.info("Training Deep Knowledge Tracing model...")
        logger.info(f"Dataset size: {len(df):,} interactions")
        
        # Prepare data
        df_clean = self._prepare_data(df, skill_col, user_col, correct_col)
        df_encoded = self._encode_skills(df_clean, skill_col)
        
        # Create sequences
        sequences = self._create_sequences(df_encoded, user_col, 'skill_id', correct_col)
        train_sequences, val_sequences = self._split_sequences(sequences, validation_split)
        
        # Prepare data for TensorFlow
        X_train, y_train, train_sample_weights = self._prepare_sequences_for_training(train_sequences)
        X_val, y_val, val_sample_weights = self._prepare_sequences_for_training(val_sequences)

        logger.info(f"Training data shape: {X_train.shape}")
        logger.info(f"Validation data shape: {X_val.shape}")

        # Build model
        self.model = self._build_model()

        # Callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=self.patience,
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-6,
                verbose=1
            )
        ]

        # Train model
        logger.info("Starting model training...")
        self.history = self.model.fit(
            X_train, y_train,
            sample_weight=train_sample_weights,
            validation_data=(X_val, y_val, val_sample_weights),
            epochs=self.max_epochs,
            batch_size=self.batch_size,
            callbacks=callbacks,
            verbose=1
        )
        
        self.is_fitted = True
        logger.info("Model training completed!")

        return self

    def predict_proba(self, user_history: List[int], skill: str, n_steps: int = 1) -> List[float]:
        """
        Predict probability of correct response for next n steps

        Args:
            user_history: List of (skill_id, correct) pairs or just correct values
            skill: Skill name for prediction
            n_steps: Number of future steps to predict

        Returns:
            List of predicted probabilities
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")

        # Encode the target skill
        if skill not in self.skill_encoders:
            logger.warning(f"Unknown skill: {skill}. Using default prediction.")
            return [0.5] * n_steps

        skill_id = self.skill_encoders[skill]

        # Prepare input sequence
        if len(user_history) == 0:
            # No history, predict based on skill only
            inputs = [skill_id]
        else:
            # Build input sequence
            inputs = []
            for i, item in enumerate(user_history):
                if isinstance(item, tuple):
                    # (skill_id, correct) format
                    s_id, correct = item
                    if i == 0:
                        inputs.append(s_id)
                    else:
                        inputs.append(s_id + (self.n_skills * user_history[i-1][1]))
                else:
                    # Just correct values, assume same skill
                    if i == 0:
                        inputs.append(skill_id)
                    else:
                        inputs.append(skill_id + (self.n_skills * user_history[i-1]))

            # Add the target skill for prediction
            if len(user_history) > 0:
                last_correct = user_history[-1][1] if isinstance(user_history[-1], tuple) else user_history[-1]
                inputs.append(skill_id + (self.n_skills * last_correct))

        # Pad sequence
        X = pad_sequences([inputs], maxlen=self.max_seq_len, padding='post', value=0)

        # Make prediction
        predictions = self.model.predict(X, verbose=0)

        # Extract the last prediction
        last_pred = predictions[0, len(inputs)-1, 0]

        # For multiple steps, we'd need to implement iterative prediction
        # For now, return the same prediction for all steps
        return [float(last_pred)] * n_steps

    def evaluate(self, df: pd.DataFrame, skill_col: str = 'skill_name',
                user_col: str = 'user_id', correct_col: str = 'correct') -> Dict[str, float]:
        """
        Evaluate the model on a dataset

        Args:
            df: Test dataset
            skill_col: Skill column name
            user_col: User column name
            correct_col: Correct column name

        Returns:
            Dictionary of evaluation metrics
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before evaluation")

        logger.info("Evaluating DKT model...")

        # Prepare data
        df_clean = self._prepare_data(df, skill_col, user_col, correct_col)
        df_encoded = self._encode_skills(df_clean, skill_col)

        # Create sequences
        sequences = self._create_sequences(df_encoded, user_col, 'skill_id', correct_col)

        # Prepare data for prediction
        X, y, _ = self._prepare_sequences_for_training(sequences)
        lengths = [seq['length'] for seq in sequences]
        
        # Make predictions
        y_pred_proba = self.model.predict(X, verbose=0)

        # Flatten predictions and targets, considering sequence lengths
        y_true_flat = []
        y_pred_flat = []

        for i, length in enumerate(lengths):
            # Only consider actual sequence length (not padding)
            seq_length = min(length, self.max_seq_len)
            y_true_flat.extend(y[i, :seq_length])
            y_pred_flat.extend(y_pred_proba[i, :seq_length, 0])

        y_true_flat = np.array(y_true_flat)
        y_pred_flat = np.array(y_pred_flat)
        y_pred_binary = (y_pred_flat > 0.5).astype(int)

        # Calculate metrics
        # Check if we have both classes for AUC calculation
        unique_classes = np.unique(y_true_flat)
        auc_score = roc_auc_score(y_true_flat, y_pred_flat) if len(unique_classes) > 1 else 0.5
        
        metrics = {
            'accuracy': accuracy_score(y_true_flat, y_pred_binary),
            'precision': precision_score(y_true_flat, y_pred_binary, zero_division=0),
            'recall': recall_score(y_true_flat, y_pred_binary, zero_division=0),
            'f1_score': f1_score(y_true_flat, y_pred_binary, zero_division=0),
            'auc': auc_score
        }

        logger.info("Evaluation completed")
        for metric, value in metrics.items():
            logger.info(f"  {metric}: {value:.4f}")

        return metrics

    def save(self, filepath: str):
        """Save the trained model"""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before saving")

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # Save model architecture and weights
        model_path = filepath.replace('.joblib', '_model.h5')
        self.model.save(model_path)

        # Save additional attributes
        import joblib
        attributes = {
            'skill_encoders': self.skill_encoders,
            'n_skills': self.n_skills,
            'vocab_size': self.vocab_size,
            'hidden_dim': self.hidden_dim,
            'num_layers': self.num_layers,
            'dropout': self.dropout,
            'max_seq_len': self.max_seq_len,
            'is_fitted': self.is_fitted
        }

        joblib.dump(attributes, filepath)
        logger.info(f"DKT model saved to {filepath}")

    @classmethod
    def load(cls, filepath: str):
        """Load a trained model"""
        import joblib

        # Load attributes
        attributes = joblib.load(filepath)

        # Create instance
        instance = cls(
            hidden_dim=attributes['hidden_dim'],
            num_layers=attributes['num_layers'],
            dropout=attributes['dropout'],
            max_seq_len=attributes['max_seq_len']
        )

        # Restore attributes
        instance.skill_encoders = attributes['skill_encoders']
        instance.n_skills = attributes['n_skills']
        instance.vocab_size = attributes['vocab_size']
        instance.is_fitted = attributes['is_fitted']

        # Load model
        model_path = filepath.replace('.joblib', '_model.h5')
        if os.path.exists(model_path):
            instance.model = tf.keras.models.load_model(model_path)

        logger.info(f"DKT model loaded from {filepath}")
        return instance

    def get_model_statistics(self) -> Dict[str, Any]:
        """Get model statistics"""
        if not self.is_fitted:
            return {}

        stats = {
            'n_skills': self.n_skills,
            'vocab_size': self.vocab_size,
            'hidden_dim': self.hidden_dim,
            'num_layers': self.num_layers,
            'max_seq_len': self.max_seq_len,
            'total_parameters': self.model.count_params() if self.model else 0
        }

        if self.history:
            stats['final_train_loss'] = self.history.history['loss'][-1]
            stats['final_val_loss'] = self.history.history['val_loss'][-1]
            stats['final_train_accuracy'] = self.history.history['accuracy'][-1]
            stats['final_val_accuracy'] = self.history.history['val_accuracy'][-1]
            stats['epochs_trained'] = len(self.history.history['loss'])

        return stats


# Utility functions for compatibility
def load_skill_builder_data(csv_path: str) -> pd.DataFrame:
    """Load the skill builder dataset"""
    df = pd.read_csv(csv_path, encoding='latin1')
    return df


def train_and_save_dkt_model(data_path: str, output_path: str, **kwargs) -> DeepKnowledgeTracing:
    """Train and save DKT model"""
    logger.info("Loading dataset...")
    df = load_skill_builder_data(data_path)

    # Default configuration
    default_config = {
        'hidden_dim': 32,
        'num_layers': 2,
        'dropout': 0.2,
        'learning_rate': 0.01,
        'batch_size': 64,
        'max_epochs': 20,
        'patience': 10,
        'max_seq_len': 100
    }

    # Update with provided kwargs
    config = {**default_config, **kwargs}

    logger.info("Initializing DKT model...")
    logger.info(f"Configuration: {config}")
    dkt = DeepKnowledgeTracing(**config)

    logger.info("Training DKT model...")
    dkt.fit(df, validation_split=0.2)

    logger.info("Saving model...")
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    dkt.save(output_path)
    logger.info(f"DKT model saved to {output_path}")

    # Print model statistics
    stats = dkt.get_model_statistics()
    logger.info("=== MODEL STATISTICS ===")
    for key, value in stats.items():
        logger.info(f"{key}: {value}")

    return dkt


if __name__ == "__main__":
    # Example usage
    DATA_PATH = "../datasets/math/skill_builder_data_corrected.csv"
    OUTPUT_PATH = "outputs/dkt_model.joblib"

    # Train and save model
    dkt_model = train_and_save_dkt_model(DATA_PATH, OUTPUT_PATH)

    # Example prediction
    user_history = [1, 0, 1, 1]  # 1=correct, 0=incorrect
    skill = 'Addition'
    probabilities = dkt_model.predict_proba(user_history, skill)
    logger.info(f"Predicted probabilities: {probabilities}")
