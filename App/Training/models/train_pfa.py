#!/usr/bin/env python3
"""
Performance Factor Analysis (PFA) Knowledge Tracing Model
=========================================================

This script implements a Performance Factor Analysis model for knowledge tracing
on student learning data. PFA models student performance based on prior success
and failure counts for each skill.

Author: ML Engineer
Date: 2025
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class PFAKnowledgeTracing:
    """
    Performance Factor Analysis Knowledge Tracing Model
    
    PFA models the probability of correct response based on:
    - Prior success count for each skill
    - Prior failure count for each skill
    - Skill difficulty parameters
    """
    
    def __init__(self):
        self.model = LogisticRegression(random_state=42, max_iter=1000)
        self.scaler = StandardScaler()
        self.skill_params = {}
        self.user_skill_history = {}
        self.is_fitted = False
        
    def create_pfa_features(self, df):
        """
        Create PFA features: success/failure counts for each user-skill combination
        """
        print("Creating PFA features...")
        
        # Sort by user, skill, and order to ensure chronological order
        df_sorted = df.sort_values(['user_id', 'skill_id', 'order_id']).copy()
        
        # Initialize feature columns
        df_sorted['success_count'] = 0
        df_sorted['failure_count'] = 0
        df_sorted['total_attempts'] = 0
        
        # Track success/failure counts for each user-skill combination
        user_skill_counts = {}
        
        for idx, row in df_sorted.iterrows():
            user_id = row['user_id']
            skill_id = row['skill_id']
            is_correct = row['correct']
            
            # Create key for user-skill combination
            key = (user_id, skill_id)
            
            # Initialize if first encounter
            if key not in user_skill_counts:
                user_skill_counts[key] = {'success': 0, 'failure': 0}
            
            # Set current counts (before this attempt)
            df_sorted.loc[idx, 'success_count'] = user_skill_counts[key]['success']
            df_sorted.loc[idx, 'failure_count'] = user_skill_counts[key]['failure']
            df_sorted.loc[idx, 'total_attempts'] = user_skill_counts[key]['success'] + user_skill_counts[key]['failure']
            
            # Update counts after this attempt
            if is_correct == 1:
                user_skill_counts[key]['success'] += 1
            else:
                user_skill_counts[key]['failure'] += 1
        
        return df_sorted
    
    def prepare_features(self, df, all_skills=None, skill_col_names=None):
        """
        Prepare feature matrix for training
        """
        # Create PFA features
        df_features = self.create_pfa_features(df)
        
        # Select features for model
        feature_cols = [
            'success_count', 'failure_count', 'total_attempts',
            'attempt_count', 'hint_count', 'ms_first_response',
            'opportunity'
        ]
        
        # Handle missing values
        df_features = df_features.dropna(subset=feature_cols + ['correct'])
        df_features = df_features.reset_index(drop=True)
        
        # Create feature matrix
        X = df_features[feature_cols].copy().reset_index(drop=True)
        y = df_features['correct'].copy().reset_index(drop=True)
        
        # Add skill-specific features (one-hot encoding for all skills)
        if all_skills is None:
            all_skills = sorted(df_features['skill_id'].unique())
        if skill_col_names is None:
            skill_col_names = [f'skill_{skill}' for skill in all_skills]
        for skill in all_skills:
            X[f'skill_{skill}'] = (df_features['skill_id'] == skill).astype(int)
        # Ensure all skill columns are present and in the same order
        X = X.reindex(columns=feature_cols + skill_col_names, fill_value=0)
        
        return X, y, df_features
    
    def fit(self, X, y, df):
        """
        Train the PFA model
        """
        print("Training PFA model...")
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Train logistic regression model
        self.model.fit(X_scaled, y)
        
        # Store skill parameters
        self.skill_params = self._extract_skill_params(X, df)
        
        # Store user skill history for analysis
        self.user_skill_history = self._create_user_skill_history(df)
        
        self.is_fitted = True
        print("Model training completed!")
        
    def _extract_skill_params(self, X, df):
        """
        Extract skill-specific parameters from the trained model
        """
        skill_params = {}
        
        # Get skill columns
        skill_cols = [col for col in X.columns if col.startswith('skill_')]
        
        for col in skill_cols:
            skill_id = col.replace('skill_', '')
            coef_idx = list(X.columns).index(col)
            skill_params[skill_id] = {
                'coefficient': self.model.coef_[0][coef_idx],
                'difficulty': -self.model.coef_[0][coef_idx],  # Higher coef = easier skill
                'frequency': df[df['skill_id'] == int(skill_id)].shape[0] if skill_id.isdigit() else 0
            }
        
        return skill_params
    
    def _create_user_skill_history(self, df):
        """
        Create user skill mastery history
        """
        user_skill_history = {}
        
        for user_id in df['user_id'].unique():
            user_data = df[df['user_id'] == user_id]
            user_skill_history[user_id] = {}
            
            for skill_id in user_data['skill_id'].unique():
                skill_data = user_data[user_data['skill_id'] == skill_id]
                user_skill_history[user_id][skill_id] = {
                    'total_attempts': len(skill_data),
                    'correct_attempts': skill_data['correct'].sum(),
                    'accuracy': skill_data['correct'].mean(),
                    'avg_attempts': skill_data['attempt_count'].mean(),
                    'mastery_level': self._calculate_mastery_level(skill_data)
                }
        
        return user_skill_history
    
    def _calculate_mastery_level(self, skill_data):
        """
        Calculate mastery level for a user-skill combination
        """
        if len(skill_data) == 0:
            return 0.0
        
        # Simple mastery calculation based on recent performance
        recent_data = skill_data.tail(5)  # Last 5 attempts
        recent_accuracy = recent_data['correct'].mean()
        
        # Weight by number of attempts
        attempt_weight = min(len(skill_data) / 10, 1.0)
        
        mastery = recent_accuracy * attempt_weight
        return round(mastery, 3)
    
    def predict(self, X):
        """
        Make predictions using the trained model
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")
        
        X_scaled = self.scaler.transform(X)
        return self.model.predict_proba(X_scaled)[:, 1]
    
    def evaluate(self, X, y):
        """
        Evaluate model performance
        """
        y_pred_proba = self.predict(X)
        y_pred = (y_pred_proba > 0.5).astype(int)
        
        metrics = {
            'accuracy': accuracy_score(y, y_pred),
            'precision': precision_score(y, y_pred),
            'recall': recall_score(y, y_pred),
            'f1': f1_score(y, y_pred),
            'auc': roc_auc_score(y, y_pred_proba)
        }
        
        return metrics

    def save(self, filepath):
        """Save the trained PFA model"""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before saving")

        import joblib
        import os

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # Save model data
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'skill_params': self.skill_params,
            'user_skill_history': self.user_skill_history,
            'is_fitted': self.is_fitted
        }

        joblib.dump(model_data, filepath)
        print(f"PFA model saved to {filepath}")

    @classmethod
    def load(cls, filepath):
        """Load a trained PFA model"""
        import joblib

        # Load model data
        model_data = joblib.load(filepath)

        # Create instance
        instance = cls()
        instance.model = model_data['model']
        instance.scaler = model_data['scaler']
        instance.skill_params = model_data['skill_params']
        instance.user_skill_history = model_data['user_skill_history']
        instance.is_fitted = model_data['is_fitted']

        print(f"PFA model loaded from {filepath}")
        return instance


def load_and_explore_data(file_path):
    """
    Load and explore the student dataset
    """
    print("Loading and exploring data...")
    
    # Load data
    df = pd.read_csv(file_path, encoding='latin1')
    
    print(f"Dataset shape: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")
    
    # Basic statistics
    print("\nBasic Statistics:")
    print(df.describe())
    
    # Check for missing values
    print(f"\nMissing values:\n{df.isnull().sum()}")
    
    # Target variable distribution
    print(f"\nTarget variable distribution:")
    print(df['correct'].value_counts())
    
    # Unique counts
    print(f"\nUnique counts:")
    print(f"Users: {df['user_id'].nunique()}")
    print(f"Skills: {df['skill_id'].nunique()}")
    print(f"Problems: {df['problem_id'].nunique()}")
    
    return df


def clean_data(df):
    """
    Clean and preprocess the data
    """
    print("Cleaning data...")
    
    # Remove rows with missing critical columns
    critical_cols = ['user_id', 'skill_id', 'correct', 'order_id']
    df_clean = df.dropna(subset=critical_cols).copy()
    
    # Fill missing values for numeric columns
    numeric_cols = ['attempt_count', 'hint_count', 'ms_first_response', 'opportunity']
    for col in numeric_cols:
        if col in df_clean.columns:
            df_clean[col] = df_clean[col].fillna(df_clean[col].median())
    
    # Convert data types
    df_clean['correct'] = df_clean['correct'].astype(int)
    df_clean['user_id'] = df_clean['user_id'].astype(str)
    df_clean['skill_id'] = df_clean['skill_id'].astype(str)
    
    # Remove outliers (optional)
    if 'ms_first_response' in df_clean.columns:
        q99 = df_clean['ms_first_response'].quantile(0.99)
        df_clean = df_clean[df_clean['ms_first_response'] <= q99]
    
    print(f"Data cleaned. Shape: {df_clean.shape}")
    return df_clean


def create_visualizations(df):
    """
    Create exploratory data visualizations
    """
    print("Creating visualizations...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. Correctness distribution
    df['correct'].value_counts().plot(kind='bar', ax=axes[0, 0])
    axes[0, 0].set_title('Correctness Distribution')
    axes[0, 0].set_xlabel('Correct (0=Wrong, 1=Right)')
    
    # 2. Skill frequency
    top_skills = df['skill_id'].value_counts().head(10)
    top_skills.plot(kind='bar', ax=axes[0, 1])
    axes[0, 1].set_title('Top 10 Skills by Frequency')
    axes[0, 1].set_xlabel('Skill ID')
    
    # 3. Attempt count distribution
    if 'attempt_count' in df.columns:
        df['attempt_count'].hist(bins=20, ax=axes[1, 0])
        axes[1, 0].set_title('Attempt Count Distribution')
        axes[1, 0].set_xlabel('Attempt Count')
    
    # 4. Correctness by skill (top 10)
    skill_accuracy = df.groupby('skill_id')['correct'].mean().sort_values(ascending=False).head(10)
    skill_accuracy.plot(kind='bar', ax=axes[1, 1])
    axes[1, 1].set_title('Accuracy by Skill (Top 10)')
    axes[1, 1].set_ylabel('Accuracy')
    
    plt.tight_layout()
    plt.show()


def analyze_model_parameters(pfa_model):
    """
    Analyze trained model parameters
    """
    print("\n" + "="*50)
    print("MODEL PARAMETER ANALYSIS")
    print("="*50)
    
    # Feature importance
    print("\nFeature Importance (Coefficients):")
    feature_names = ['success_count', 'failure_count', 'total_attempts', 
                    'attempt_count', 'hint_count', 'ms_first_response', 'opportunity']
    
    for i, feature in enumerate(feature_names):
        if i < len(pfa_model.model.coef_[0]):
            coef = pfa_model.model.coef_[0][i]
            print(f"{feature}: {coef:.4f}")
    
    # Skill difficulty analysis
    print(f"\nSkill Difficulty Analysis:")
    print(f"Total skills analyzed: {len(pfa_model.skill_params)}")
    
    if pfa_model.skill_params:
        sorted_skills = sorted(pfa_model.skill_params.items(), 
                              key=lambda x: x[1]['difficulty'], reverse=True)
        
        print("\nTop 5 Most Difficult Skills:")
        for skill_id, params in sorted_skills[:5]:
            print(f"Skill {skill_id}: Difficulty = {params['difficulty']:.4f}, "
                  f"Frequency = {params['frequency']}")
        
        print("\nTop 5 Easiest Skills:")
        for skill_id, params in sorted_skills[-5:]:
            print(f"Skill {skill_id}: Difficulty = {params['difficulty']:.4f}, "
                  f"Frequency = {params['frequency']}")


def analyze_user_mastery(pfa_model, top_n=5):
    """
    Analyze user mastery levels
    """
    print(f"\n" + "="*50)
    print("USER MASTERY ANALYSIS")
    print("="*50)
    
    # Calculate overall user performance
    user_performance = {}
    for user_id, skills in pfa_model.user_skill_history.items():
        total_attempts = sum(skill['total_attempts'] for skill in skills.values())
        total_correct = sum(skill['correct_attempts'] for skill in skills.values())
        avg_mastery = np.mean([skill['mastery_level'] for skill in skills.values()])
        
        user_performance[user_id] = {
            'total_attempts': total_attempts,
            'accuracy': total_correct / total_attempts if total_attempts > 0 else 0,
            'skills_practiced': len(skills),
            'avg_mastery': avg_mastery
        }
    
    # Sort by average mastery
    sorted_users = sorted(user_performance.items(), 
                         key=lambda x: x[1]['avg_mastery'], reverse=True)
    
    print(f"\nTop {top_n} Users by Mastery Level:")
    for user_id, performance in sorted_users[:top_n]:
        print(f"User {user_id}: Mastery = {performance['avg_mastery']:.3f}, "
              f"Accuracy = {performance['accuracy']:.3f}, "
              f"Skills = {performance['skills_practiced']}")


def generate_training_stats(df, pfa_model):
    """
    Generate comprehensive training statistics
    """
    print(f"\n" + "="*50)
    print("TRAINING STATISTICS")
    print("="*50)
    
    # Overall statistics
    print(f"Total interactions: {len(df)}")
    print(f"Total users: {df['user_id'].nunique()}")
    print(f"Total skills: {df['skill_id'].nunique()}")
    print(f"Total problems: {df['problem_id'].nunique()}")
    print(f"Overall accuracy: {df['correct'].mean():.3f}")
    
    # Skill statistics
    print(f"\nSkill Statistics:")
    skill_stats = df.groupby('skill_name').agg({
        'correct': ['count', 'mean', 'std'],
        'attempt_count': 'mean',
        'user_id': 'nunique'
    }).round(3)
    
    skill_stats.columns = ['Total_Interactions', 'Accuracy', 'Accuracy_Std', 
                          'Avg_Attempts', 'Unique_Users']
    
    print(skill_stats.head(10))
    
    # Problem statistics
    print(f"\nProblem Statistics (Top 10):")
    problem_stats = df.groupby('problem_id').agg({
        'correct': ['count', 'mean'],
        'attempt_count': 'mean',
        'user_id': 'nunique'
    }).round(3)
    
    problem_stats.columns = ['Total_Interactions', 'Accuracy', 'Avg_Attempts', 'Unique_Users']
    problem_stats = problem_stats.sort_values('Total_Interactions', ascending=False)
    
    print(problem_stats.head(10))
    
    # Student statistics
    print(f"\nStudent Statistics (Top 10 by activity):")
    student_stats = df.groupby('user_id').agg({
        'correct': ['count', 'mean'],
        'skill_id': 'nunique',
        'problem_id': 'nunique'
    }).round(3)
    
    student_stats.columns = ['Total_Interactions', 'Accuracy', 'Skills_Practiced', 'Problems_Solved']
    student_stats = student_stats.sort_values('Total_Interactions', ascending=False)
    
    print(student_stats.head(10))


def prediction_examples(pfa_model, X_test, df_test, n_examples=5):
    """
    Show prediction examples for trained skills
    """
    print(f"\n" + "="*50)
    print("PREDICTION EXAMPLES")
    print("="*50)
    
    # Make predictions
    predictions = pfa_model.predict(X_test)
    
    # Select random examples
    indices = np.random.choice(len(X_test), n_examples, replace=False)
    
    print(f"Showing {n_examples} prediction examples:")
    print()
    
    for i, idx in enumerate(indices):
        actual = df_test.iloc[idx]['correct']
        predicted_prob = predictions[idx]
        predicted_class = 1 if predicted_prob > 0.5 else 0
        
        user_id = df_test.iloc[idx]['user_id']
        skill_id = df_test.iloc[idx]['skill_id']
        success_count = X_test.iloc[idx]['success_count']
        failure_count = X_test.iloc[idx]['failure_count']
        
        print(f"Example {i+1}:")
        print(f"  User: {user_id}, Skill: {skill_id}")
        print(f"  Prior Success: {success_count}, Prior Failures: {failure_count}")
        print(f"  Predicted Probability: {predicted_prob:.3f}")
        print(f"  Predicted Class: {predicted_class}")
        print(f"  Actual: {actual}")
        print(f"  Correct: {'✓' if predicted_class == actual else '✗'}")
        print()


def main():
    """
    Main execution function
    """
    print("Performance Factor Analysis Knowledge Tracing")
    print("=" * 50)
    
    # 1. Load and explore data
    file_path = "../datasets/math/skill_builder_data_corrected.csv"  # Updated to correct file path
    df = load_and_explore_data(file_path)
    # df= df.head(2500)
    
    # 2. Clean data
    df_clean = clean_data(df)
    
    # 3. Create visualizations
    create_visualizations(df_clean)
    
    # 4. Create complete feature matrix with all skills before splitting
    print("\nCreating complete feature matrix...")
    
    # Initialize PFA model
    pfa_model = PFAKnowledgeTracing()
    
    # Determine all unique skills for one-hot encoding
    all_skills = sorted(df_clean['skill_id'].unique())
    skill_col_names = [f'skill_{skill}' for skill in all_skills]
    
    print(f"Total unique skills: {len(all_skills)}")
    print(f"Creating {len(skill_col_names)} skill columns")
    
    # Prepare complete feature matrix with all skills
    X, y, df_features = pfa_model.prepare_features(df_clean, all_skills=all_skills, skill_col_names=skill_col_names)
    
    print(f"Feature matrix shape: {X.shape}")
    print(f"Feature columns: {list(X.columns)}")
    
    # 5. Split data (80% train, 20% test) - now X already has all skill columns
    print("\nSplitting data for train/test...")
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # Split the dataframe for analysis
    df_train = df_features.iloc[X_train.index]
    df_test = df_features.iloc[X_test.index]
    
    print(f"Training set size: {len(X_train)}")
    print(f"Test set size: {len(X_test)}")
    print(f"Training features: {X_train.shape[1]}")
    print(f"Test features: {X_test.shape[1]}")
    
    # Verify that train and test have the same columns
    train_cols = set(X_train.columns)
    test_cols = set(X_test.columns)
    if train_cols != test_cols:
        print(f"WARNING: Column mismatch!")
        print(f"Missing in test: {train_cols - test_cols}")
        print(f"Missing in train: {test_cols - train_cols}")
    else:
        print("✓ Train and test have identical feature columns")
    
    # 6. Train PFA model
    pfa_model.fit(X_train, y_train, df_train)
    
    # 7. Evaluate model
    print("\nEvaluating model performance...")
    train_metrics = pfa_model.evaluate(X_train, y_train)
    test_metrics = pfa_model.evaluate(X_test, y_test)
    
    print(f"\nTraining Metrics:")
    for metric, value in train_metrics.items():
        print(f"  {metric}: {value:.4f}")
    
    print(f"\nTesting Metrics:")
    for metric, value in test_metrics.items():
        print(f"  {metric}: {value:.4f}")
    
    # 8. Analyze model parameters
    analyze_model_parameters(pfa_model)
    
    # 9. Analyze user mastery
    analyze_user_mastery(pfa_model)
    
    # 10. Generate training statistics
    generate_training_stats(df_clean, pfa_model)
    
    # 11. Show prediction examples
    prediction_examples(pfa_model, X_test, df_test)
    
    print("\n" + "="*50)
    print("ANALYSIS COMPLETE")
    print("="*50)


if __name__ == "__main__":
    main()