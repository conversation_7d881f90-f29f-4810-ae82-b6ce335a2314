#!/usr/bin/env python3
"""
Ensemble Knowledge Tracing Training Script
==========================================

This script creates and trains ensemble models using stacking and voting
approaches for knowledge tracing. It combines BKT, PFA, and DKT models
to create a more robust prediction system.

Usage:
    python train_ensemble.py

Requirements:
    - Pre-trained BKT, PFA, and DKT models in outputs directory
    - TensorFlow for DKT model loading
    - scikit-learn for ensemble methods
"""

import os
import sys
import pandas as pd
import numpy as np
import joblib
import json
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Any
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.model_selection import cross_val_score
import warnings
warnings.filterwarnings('ignore')

# Add pipeline directory to path
sys.path.append('../pipeline')
sys.path.append('../models')

from kt_ensemble import KTModelEnsemble
from kt_evaluation import KTEvaluator
from train_bkt import BayesianKnowledgeTracer
from train_pfa import PFAKnowledgeTracing
from train_dkt import DeepKnowledgeTracing

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ensemble_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnsembleTrainer:
    """
    Comprehensive ensemble trainer for knowledge tracing models
    """
    
    def __init__(self, models_dir: str = "./outputs",
                 data_path: str = "../datasets/math/skill_builder_data_corrected.csv",
                 test_sample_size: int = None):
        """
        Initialize ensemble trainer

        Args:
            models_dir: Directory containing pre-trained models
            data_path: Path to the dataset
            test_sample_size: Number of samples to use for testing (None = use entire test set)
        """
        self.models_dir = models_dir
        self.data_path = data_path
        self.models = {}
        self.ensemble_models = {}
        self.metrics = {}
        self.test_sample_size = test_sample_size  # None means use entire dataset
        
    def load_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Load and prepare dataset"""
        logger.info("Loading dataset...")
        
        # Load data
        df = pd.read_csv(self.data_path, encoding='latin1')
        logger.info(f"Dataset loaded: {df.shape}")
        
        # Clean data
        df_clean = self._clean_data(df)
        
        # Split data (80% train, 20% test)
        train_size = int(0.8 * len(df_clean))
        train_df = df_clean.iloc[:train_size].copy()
        test_df = df_clean.iloc[train_size:].copy()
        
        logger.info(f"Train set: {train_df.shape}")
        logger.info(f"Test set: {test_df.shape}")
        
        return df_clean, train_df, test_df
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and prepare data"""
        logger.info("Cleaning data...")
        
        # Remove rows with missing values in key columns
        required_cols = ['user_id', 'skill_name', 'correct']
        df_clean = df.dropna(subset=required_cols).copy()
        
        # Ensure correct column is binary
        df_clean['correct'] = df_clean['correct'].astype(int)
        
        # Sort by user and timestamp if available
        if 'timestamp' in df_clean.columns:
            df_clean = df_clean.sort_values(['user_id', 'timestamp'])
        else:
            df_clean = df_clean.sort_values(['user_id'])
        
        # Reset index
        df_clean = df_clean.reset_index(drop=True)
        
        logger.info(f"Data cleaned: {df_clean.shape}")
        return df_clean
    
    def load_trained_models(self) -> Dict[str, Any]:
        """Load pre-trained models"""
        logger.info("Loading pre-trained models...")
        
        models = {}
        
        # Load BKT model
        bkt_path = os.path.join(self.models_dir, "bkt_model.joblib")
        if os.path.exists(bkt_path):
            models['bkt'] = BayesianKnowledgeTracer.load(bkt_path)
            logger.info("✅ BKT model loaded")
        else:
            logger.warning(f"❌ BKT model not found at {bkt_path}")
        
        # Load PFA model
        pfa_path = os.path.join(self.models_dir, "pfa_model.joblib")
        if os.path.exists(pfa_path):
            models['pfa'] = joblib.load(pfa_path)
            logger.info("✅ PFA model loaded")
        else:
            logger.warning(f"❌ PFA model not found at {pfa_path}")
        
        # Load DKT model
        dkt_path = os.path.join(self.models_dir, "dkt_model.joblib")
        if os.path.exists(dkt_path):
            models['dkt'] = joblib.load(dkt_path)
            logger.info("✅ DKT model loaded")
        else:
            logger.warning(f"❌ DKT model not found at {dkt_path}")
        
        self.models = models
        logger.info(f"Loaded {len(models)} models: {list(models.keys())}")
        return models
    
    def evaluate_individual_models(self, test_df: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """Evaluate individual models on test set"""
        logger.info("Evaluating individual models...")

        # Use specified number of samples or entire test set
        if self.test_sample_size is None:
            test_sample = test_df.copy()
            logger.info(f"Using entire test set: {len(test_sample)} samples for evaluation")
        else:
            test_sample = test_df.head(self.test_sample_size).copy()
            logger.info(f"Using {len(test_sample)} samples for evaluation")
        
        individual_metrics = {}
        
        for model_name, model in self.models.items():
            logger.info(f"Evaluating {model_name} model...")
            
            try:
                # Get predictions based on model type
                if model_name == 'bkt':
                    predictions = self._get_bkt_predictions(model, test_sample)
                elif model_name == 'pfa':
                    predictions = self._get_pfa_predictions(model, test_sample)
                elif model_name == 'dkt':
                    predictions = self._get_dkt_predictions(model, test_sample)
                else:
                    logger.warning(f"Unknown model type: {model_name}")
                    continue
                
                # Calculate metrics
                y_true = test_sample['correct'].values
                metrics = self._calculate_metrics(y_true, predictions)
                individual_metrics[model_name] = metrics
                
                logger.info(f"{model_name} metrics: {metrics}")
                
            except Exception as e:
                logger.error(f"Error evaluating {model_name}: {e}")
                individual_metrics[model_name] = {'error': str(e)}
        
        return individual_metrics
    
    def _get_bkt_predictions(self, model, test_df: pd.DataFrame) -> np.ndarray:
        """Get predictions from BKT model"""
        predictions = []
        
        for _, row in test_df.iterrows():
            try:
                # Create user history (simplified)
                user_history = [row['correct']]  # Simplified for demo
                skill = row['skill_name']
                
                # Get prediction
                pred_proba = model.predict_proba(user_history, skill)
                if isinstance(pred_proba, (list, np.ndarray)):
                    pred = pred_proba[-1] if len(pred_proba) > 0 else 0.5
                else:
                    pred = pred_proba
                
                predictions.append(pred)
                
            except Exception as e:
                logger.warning(f"BKT prediction error: {e}")
                predictions.append(0.5)  # Default prediction
        
        return np.array(predictions)
    
    def _get_pfa_predictions(self, model, test_df: pd.DataFrame) -> np.ndarray:
        """Get predictions from PFA model"""
        predictions = []
        
        for _, row in test_df.iterrows():
            try:
                # Create user history (simplified)
                user_history = [row['correct']]  # Simplified for demo
                skill = row['skill_name']
                
                # Get prediction
                pred = model.predict_proba(user_history, skill)
                if isinstance(pred, (list, np.ndarray)):
                    pred = pred[-1] if len(pred) > 0 else 0.5
                
                predictions.append(pred)
                
            except Exception as e:
                # logger.warning(f"PFA prediction error: {e}")
                predictions.append(0.5)  # Default prediction
        
        return np.array(predictions)
    
    def _get_dkt_predictions(self, model, test_df: pd.DataFrame) -> np.ndarray:
        """Get predictions from DKT model"""
        predictions = []
        
        for _, row in test_df.iterrows():
            try:
                # Create user history (simplified)
                user_history = [row['correct']]  # Simplified for demo
                skill = row['skill_name']
                
                # Get prediction
                pred = model.predict_proba(user_history, skill)
                if isinstance(pred, (list, np.ndarray)):
                    pred = pred[-1] if len(pred) > 0 else 0.5
                
                predictions.append(pred)
                
            except Exception as e:
                # logger.warning(f"DKT prediction error: {e}")
                predictions.append(0.5)  # Default prediction
        
        return np.array(predictions)
    
    def _calculate_metrics(self, y_true: np.ndarray, y_pred_proba: np.ndarray) -> Dict[str, float]:
        """Calculate evaluation metrics"""
        y_pred = (y_pred_proba > 0.5).astype(int)
        
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, zero_division=0),
            'recall': recall_score(y_true, y_pred, zero_division=0),
            'f1_score': f1_score(y_true, y_pred, zero_division=0),
            'auc_roc': roc_auc_score(y_true, y_pred_proba) if len(np.unique(y_true)) > 1 else 0.0
        }
        
        return {k: round(v, 4) for k, v in metrics.items()}

    def train_ensemble_models(self, train_df: pd.DataFrame, test_df: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """Train different ensemble models"""
        logger.info("Training ensemble models...")

        if len(self.models) < 2:
            logger.error("Need at least 2 models for ensemble")
            return {}

        # Use specified number of samples or entire dataset
        if self.test_sample_size is None:
            test_sample = test_df.copy()
            train_sample = train_df.copy()
            logger.info(f"Using entire dataset: {len(train_sample)} train, {len(test_sample)} test samples")
        else:
            test_sample = test_df.head(self.test_sample_size).copy()
            train_sample = train_df.head(self.test_sample_size * 4).copy()  # Use more for training
            logger.info(f"Using {len(train_sample)} train, {len(test_sample)} test samples")

        ensemble_metrics = {}

        # Create ensemble with available models
        model_list = list(self.models.values())
        model_names = list(self.models.keys())

        # 1. Weighted Average Ensemble
        logger.info("Training weighted average ensemble...")
        try:
            ensemble_weighted = KTModelEnsemble(model_list, model_names)
            metrics_weighted = ensemble_weighted.train_ensemble(
                train_sample, test_sample, ensemble_type='weighted_average'
            )
            ensemble_metrics['weighted_average'] = metrics_weighted
            self.ensemble_models['weighted_average'] = ensemble_weighted
            logger.info(f"Weighted average metrics: {metrics_weighted}")
        except Exception as e:
            logger.error(f"Error training weighted average ensemble: {e}")

        # 2. Stacking Ensemble
        logger.info("Training stacking ensemble...")
        try:
            ensemble_stacking = KTModelEnsemble(model_list, model_names)
            metrics_stacking = ensemble_stacking.train_ensemble(
                train_sample, test_sample, ensemble_type='stacking'
            )
            ensemble_metrics['stacking'] = metrics_stacking
            self.ensemble_models['stacking'] = ensemble_stacking
            logger.info(f"Stacking metrics: {metrics_stacking}")
        except Exception as e:
            logger.error(f"Error training stacking ensemble: {e}")

        # 3. Voting Ensemble
        logger.info("Training voting ensemble...")
        try:
            ensemble_voting = KTModelEnsemble(model_list, model_names)
            metrics_voting = ensemble_voting.train_ensemble(
                train_sample, test_sample, ensemble_type='voting'
            )
            ensemble_metrics['voting'] = metrics_voting
            self.ensemble_models['voting'] = ensemble_voting
            logger.info(f"Voting metrics: {metrics_voting}")
        except Exception as e:
            logger.error(f"Error training voting ensemble: {e}")

        return ensemble_metrics

    def save_ensemble_models(self, output_dir: str = "../models/outputs") -> None:
        """Save trained ensemble models"""
        logger.info(f"Saving ensemble models to {output_dir}...")

        os.makedirs(output_dir, exist_ok=True)

        for ensemble_name, ensemble_model in self.ensemble_models.items():
            try:
                ensemble_path = os.path.join(output_dir, f"ensemble_{ensemble_name}_model.joblib")
                ensemble_model.save(ensemble_path)
                logger.info(f"✅ Saved {ensemble_name} ensemble to {ensemble_path}")
            except Exception as e:
                logger.error(f"❌ Error saving {ensemble_name} ensemble: {e}")

        # Save the best performing ensemble as the main ensemble model
        if self.ensemble_models:
            best_ensemble_name = self._find_best_ensemble()
            if best_ensemble_name:
                best_ensemble = self.ensemble_models[best_ensemble_name]
                main_ensemble_path = os.path.join(output_dir, "ensemble_model.joblib")
                best_ensemble.save(main_ensemble_path)
                logger.info(f"✅ Saved best ensemble ({best_ensemble_name}) as main ensemble model")

    def _find_best_ensemble(self) -> str:
        """Find the best performing ensemble based on metrics"""
        if not self.metrics:
            return None

        best_ensemble = None
        best_score = 0

        for ensemble_name, metrics in self.metrics.items():
            if ensemble_name.startswith('ensemble_'):
                # Use F1 score as the primary metric
                score = metrics.get('f1_score', 0)
                if score > best_score:
                    best_score = score
                    best_ensemble = ensemble_name.replace('ensemble_', '')

        return best_ensemble

    def generate_comparison_report(self) -> Dict[str, Any]:
        """Generate comprehensive comparison report"""
        logger.info("Generating comparison report...")

        report = {
            'timestamp': datetime.now().isoformat(),
            'test_sample_size': self.test_sample_size if self.test_sample_size is not None else 'entire_dataset',
            'models_evaluated': list(self.models.keys()),
            'ensemble_methods': list(self.ensemble_models.keys()),
            'individual_model_metrics': {},
            'ensemble_metrics': {},
            'best_model': None,
            'best_ensemble': None,
            'summary': {}
        }

        # Add individual model metrics
        for model_name, metrics in self.metrics.items():
            if not model_name.startswith('ensemble_'):
                report['individual_model_metrics'][model_name] = metrics

        # Add ensemble metrics
        for model_name, metrics in self.metrics.items():
            if model_name.startswith('ensemble_'):
                ensemble_name = model_name.replace('ensemble_', '')
                report['ensemble_metrics'][ensemble_name] = metrics

        # Find best individual model
        best_individual_score = 0
        best_individual = None
        for model_name, metrics in report['individual_model_metrics'].items():
            score = metrics.get('f1_score', 0)
            if score > best_individual_score:
                best_individual_score = score
                best_individual = model_name

        report['best_model'] = best_individual
        report['best_ensemble'] = self._find_best_ensemble()

        # Generate summary
        report['summary'] = {
            'total_models_trained': len(self.models),
            'total_ensembles_trained': len(self.ensemble_models),
            'best_individual_f1': best_individual_score,
            'best_ensemble_f1': max([m.get('f1_score', 0) for m in report['ensemble_metrics'].values()]) if report['ensemble_metrics'] else 0
        }

        return report

    def save_report(self, report: Dict[str, Any], output_dir: str = "../models/outputs") -> None:
        """Save comparison report"""
        os.makedirs(output_dir, exist_ok=True)

        # Save JSON report
        report_path = os.path.join(output_dir, "ensemble_comparison_report.json")
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        logger.info(f"✅ Report saved to {report_path}")

        # Save human-readable summary
        summary_path = os.path.join(output_dir, "ensemble_summary.txt")
        with open(summary_path, 'w') as f:
            f.write("ENSEMBLE MODEL COMPARISON REPORT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Generated: {report['timestamp']}\n")
            f.write(f"Test Sample Size: {report['test_sample_size']:,}\n\n")

            f.write("INDIVIDUAL MODEL PERFORMANCE:\n")
            f.write("-" * 30 + "\n")
            for model_name, metrics in report['individual_model_metrics'].items():
                f.write(f"{model_name.upper()}:\n")
                for metric, value in metrics.items():
                    f.write(f"  {metric}: {value:.4f}\n")
                f.write("\n")

            f.write("ENSEMBLE MODEL PERFORMANCE:\n")
            f.write("-" * 30 + "\n")
            for ensemble_name, metrics in report['ensemble_metrics'].items():
                f.write(f"{ensemble_name.upper()}:\n")
                for metric, value in metrics.items():
                    f.write(f"  {metric}: {value:.4f}\n")
                f.write("\n")

            f.write("SUMMARY:\n")
            f.write("-" * 10 + "\n")
            f.write(f"Best Individual Model: {report['best_model']} (F1: {report['summary']['best_individual_f1']:.4f})\n")
            f.write(f"Best Ensemble Model: {report['best_ensemble']} (F1: {report['summary']['best_ensemble_f1']:.4f})\n")

        logger.info(f"✅ Summary saved to {summary_path}")

    def run_complete_training(self) -> Dict[str, Any]:
        """Run complete ensemble training pipeline"""
        logger.info("🚀 Starting complete ensemble training pipeline...")

        try:
            # Load data
            df_full, train_df, test_df = self.load_data()

            # Load pre-trained models
            models = self.load_trained_models()

            if not models:
                logger.error("No models loaded. Cannot proceed with ensemble training.")
                return {}

            # Evaluate individual models
            individual_metrics = self.evaluate_individual_models(test_df)
            self.metrics.update(individual_metrics)

            # Train ensemble models
            ensemble_metrics = self.train_ensemble_models(train_df, test_df)

            # Add ensemble prefix to metrics
            for ensemble_name, metrics in ensemble_metrics.items():
                self.metrics[f'ensemble_{ensemble_name}'] = metrics

            # Save ensemble models
            self.save_ensemble_models()

            # Generate and save report
            report = self.generate_comparison_report()
            self.save_report(report)

            logger.info("✅ Complete ensemble training pipeline finished successfully!")
            return report

        except Exception as e:
            logger.error(f"❌ Pipeline failed: {e}")
            raise


def main():
    """Main execution function"""
    print("🎯 ENSEMBLE KNOWLEDGE TRACING TRAINING")
    print("=" * 50)

    # Initialize trainer
    trainer = EnsembleTrainer()

    # Run complete training
    report = trainer.run_complete_training()

    # Print summary
    if report:
        print("\n📊 TRAINING SUMMARY")
        print("-" * 20)
        print(f"Models trained: {report['summary']['total_models_trained']}")
        print(f"Ensembles trained: {report['summary']['total_ensembles_trained']}")
        print(f"Best individual model: {report['best_model']} (F1: {report['summary']['best_individual_f1']:.4f})")
        print(f"Best ensemble model: {report['best_ensemble']} (F1: {report['summary']['best_ensemble_f1']:.4f})")
        print("\n✅ Check outputs directory for saved models and detailed reports!")


if __name__ == "__main__":
    main()
