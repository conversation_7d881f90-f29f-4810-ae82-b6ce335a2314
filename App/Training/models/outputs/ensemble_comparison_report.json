{"timestamp": "2025-07-19T16:59:40.377050", "test_sample_size": 10000, "models_evaluated": ["bkt", "pfa", "dkt"], "ensemble_methods": ["weighted_average", "stacking", "voting"], "individual_model_metrics": {"bkt": {"accuracy": 0.5889, "precision": 0.5892, "recall": 0.8842, "f1_score": 0.7072, "auc_roc": 0.6079}, "pfa": {"accuracy": 0.4385, "precision": 0.0, "recall": 0.0, "f1_score": 0.0, "auc_roc": 0.5}, "dkt": {"accuracy": 0.4385, "precision": 0.0, "recall": 0.0, "f1_score": 0.0, "auc_roc": 0.5}}, "ensemble_metrics": {"weighted_average": {"accuracy": 0.6867, "precision": 0.6917490729295427, "recall": 0.7973285841495993, "f1_score": 0.7407958964176388, "auc": 0.719063958630115}, "stacking": {"accuracy": 0.5764, "precision": 0.5706817016914403, "recall": 0.9914514692787177, "f1_score": 0.7243981782693559, "auc": 0.724433311570754}, "voting": {"accuracy": 0.5531, "precision": 0.7019027484143763, "recall": 0.35476402493321463, "f1_score": 0.4713119602507985, "auc": 0.5809167901176906}}, "best_model": "bkt", "best_ensemble": "weighted_average", "summary": {"total_models_trained": 3, "total_ensembles_trained": 3, "best_individual_f1": 0.7072, "best_ensemble_f1": 0.7407958964176388}}