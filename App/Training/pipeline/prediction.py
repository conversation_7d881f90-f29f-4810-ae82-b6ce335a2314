#!/usr/bin/env python3
"""
Knowledge Tracing Prediction Module
===================================

This module uses trained knowledge tracing models (BKT, PFA, DKT, and Ensemble)
to predict the knowledge state of students for given skills.

Usage:
    python prediction.py

Examples:
    # Load models and make predictions
    predictor = KTPredictor()
    predictor.load_models()
    
    # Predict for a student
    probability = predictor.predict_student_performance(
        user_history=[1, 0, 1, 1],  # correct/incorrect sequence
        skill="Addition",
        model_type="ensemble"
    )
"""

import os
import sys
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Union, Tuple
import logging
import warnings
warnings.filterwarnings('ignore')

# Add the models directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'models'))

# Import models
from train_bkt import BayesianKnowledgeTracer
from train_pfa import PFAKnowledgeTracing
from train_dkt import DeepKnowledgeTracing
from kt_ensemble import KTModelEnsemble

logger = logging.getLogger(__name__)

class KTPredictor:
    """Knowledge Tracing Predictor for making predictions with trained models"""
    
    def __init__(self, models_dir: str = "../models/outputs"):
        """
        Initialize the predictor
        
        Args:
            models_dir: Directory containing trained models
        """
        self.models_dir = models_dir
        self.models = {}
        self.model_paths = {
            'bkt': os.path.join(models_dir, 'bkt_model.joblib'),
            'pfa': os.path.join(models_dir, 'pfa_model.joblib'),
            'dkt': os.path.join(models_dir, 'dkt_model.h5'),
            'ensemble': os.path.join(models_dir, 'ensemble_model.joblib')
        }
        
    def load_models(self, model_types: List[str] = None) -> Dict[str, bool]:
        """
        Load trained models
        
        Args:
            model_types: List of model types to load. If None, loads all available.
            
        Returns:
            Dictionary indicating which models were loaded successfully
        """
        if model_types is None:
            model_types = ['bkt', 'pfa', 'dkt', 'ensemble']
        
        load_status = {}
        
        for model_type in model_types:
            try:
                model_path = self.model_paths.get(model_type)
                if model_path and os.path.exists(model_path):
                    
                    if model_type == 'bkt':
                        self.models[model_type] = BayesianKnowledgeTracer.load(model_path)
                    elif model_type == 'pfa':
                        self.models[model_type] = PFAKnowledgeTracing.load(model_path)
                    elif model_type == 'dkt':
                        self.models[model_type] = DeepKnowledgeTracing.load(model_path)
                    elif model_type == 'ensemble':
                        # For ensemble, we need to load individual models first
                        individual_models = []
                        for base_type in ['bkt', 'pfa', 'dkt']:
                            if base_type in self.models:
                                individual_models.append(self.models[base_type])
                        
                        if len(individual_models) >= 2:  # Need at least 2 models for ensemble
                            self.models[model_type] = KTModelEnsemble.load(model_path, individual_models)
                        else:
                            logger.warning("Cannot load ensemble: insufficient base models")
                            load_status[model_type] = False
                            continue
                    
                    load_status[model_type] = True
                    logger.info(f"Successfully loaded {model_type} model")
                    
                else:
                    logger.warning(f"Model file not found: {model_path}")
                    load_status[model_type] = False
                    
            except Exception as e:
                logger.error(f"Error loading {model_type} model: {e}")
                load_status[model_type] = False
        
        logger.info(f"Loaded {sum(load_status.values())} out of {len(model_types)} models")
        return load_status
    
    def predict_student_performance(self, 
                                  user_history: List[Union[int, Tuple[str, int]]], 
                                  skill: str,
                                  model_type: str = "ensemble",
                                  user_id: str = None,
                                  n_steps: int = 1) -> Union[float, List[float]]:
        """
        Predict student performance for a given skill
        
        Args:
            user_history: List of previous interactions. Can be:
                         - List of correct/incorrect (1/0) values
                         - List of (skill, correct) tuples
            skill: Target skill name
            model_type: Type of model to use ('bkt', 'pfa', 'dkt', 'ensemble')
            user_id: User ID (optional, used for some models)
            n_steps: Number of future steps to predict
            
        Returns:
            Predicted probability(ies) of correct response
        """
        if model_type not in self.models:
            raise ValueError(f"Model {model_type} not loaded. Available models: {list(self.models.keys())}")
        
        model = self.models[model_type]
        
        try:
            if model_type == 'ensemble':
                # Ensemble model has a different interface
                prediction = model.predict(user_history, skill, user_id)
                return [prediction] * n_steps if n_steps > 1 else prediction
            elif model_type == 'pfa':
                # PFA model has a different interface
                predictions = model.predict(user_history)
                return predictions if n_steps > 1 else predictions[0]        
            else:
                # Individual models
                predictions = model.predict_proba(user_history, skill, n_steps)
                return predictions if n_steps > 1 else predictions[0]
                
        except Exception as e:
            logger.error(f"Error making prediction with {model_type}: {e}")
            return [0.5] * n_steps if n_steps > 1 else 0.5
    
    def compare_model_predictions(self, 
                                user_history: List[Union[int, Tuple[str, int]]], 
                                skill: str,
                                user_id: str = None) -> Dict[str, float]:
        """
        Compare predictions from all loaded models
        
        Args:
            user_history: List of previous interactions
            skill: Target skill name
            user_id: User ID (optional)
            
        Returns:
            Dictionary of predictions from each model
        """
        predictions = {}
        
        for model_type in self.models.keys():
            try:
                pred = self.predict_student_performance(
                    user_history, skill, model_type, user_id, n_steps=1
                )
                predictions[model_type] = pred
            except Exception as e:
                logger.warning(f"Error getting prediction from {model_type}: {e}")
                predictions[model_type] = 0.5
        
        return predictions
    
    def predict_learning_trajectory(self, 
                                  user_history: List[Union[int, Tuple[str, int]]], 
                                  skill: str,
                                  n_future_interactions: int = 5,
                                  model_type: str = "ensemble") -> List[float]:
        """
        Predict learning trajectory for multiple future interactions
        
        Args:
            user_history: List of previous interactions
            skill: Target skill name
            n_future_interactions: Number of future interactions to predict
            model_type: Type of model to use
            
        Returns:
            List of predicted probabilities for future interactions
        """
        if model_type not in self.models:
            raise ValueError(f"Model {model_type} not loaded")
        
        # For now, return the same prediction for all future steps
        # In a more sophisticated implementation, we would simulate
        # the learning process step by step
        base_prediction = self.predict_student_performance(
            user_history, skill, model_type, n_steps=1
        )
        
        # Simple trajectory: assume gradual improvement
        trajectory = []
        for i in range(n_future_interactions):
            # Add small improvement over time
            improvement = 0.02 * i  # 2% improvement per interaction
            pred = min(base_prediction + improvement, 0.95)  # Cap at 95%
            trajectory.append(pred)
        
        return trajectory
    
    def get_skill_difficulty_ranking(self, skills: List[str], 
                                   sample_history: List[int] = None) -> List[Tuple[str, float]]:
        """
        Rank skills by difficulty based on model predictions
        
        Args:
            skills: List of skill names
            sample_history: Sample user history to use for predictions
            
        Returns:
            List of (skill, difficulty_score) tuples, sorted by difficulty
        """
        if sample_history is None:
            sample_history = [1, 0, 1]  # Default history
        
        skill_difficulties = []
        
        for skill in skills:
            try:
                # Use ensemble model if available, otherwise use any available model
                model_type = 'ensemble' if 'ensemble' in self.models else list(self.models.keys())[0]
                
                prediction = self.predict_student_performance(
                    sample_history, skill, model_type
                )
                
                # Difficulty is inverse of predicted success probability
                difficulty = 1.0 - prediction
                skill_difficulties.append((skill, difficulty))
                
            except Exception as e:
                logger.warning(f"Error predicting difficulty for skill {skill}: {e}")
                skill_difficulties.append((skill, 0.5))  # Default difficulty
        
        # Sort by difficulty (highest first)
        skill_difficulties.sort(key=lambda x: x[1], reverse=True)
        return skill_difficulties
    
    def recommend_next_problems(self, 
                              user_history: List[Union[int, Tuple[str, int]]], 
                              available_skills: List[str],
                              target_difficulty: float = 0.7,
                              n_recommendations: int = 5) -> List[Tuple[str, float]]:
        """
        Recommend next problems/skills based on target difficulty
        
        Args:
            user_history: User's learning history
            available_skills: List of available skills
            target_difficulty: Target difficulty level (0.0 to 1.0)
            n_recommendations: Number of recommendations to return
            
        Returns:
            List of (skill, predicted_probability) tuples
        """
        skill_predictions = []
        
        for skill in available_skills:
            try:
                prediction = self.predict_student_performance(
                    user_history, skill, 'ensemble' if 'ensemble' in self.models else list(self.models.keys())[0]
                )
                
                # Calculate how close the prediction is to target difficulty
                difficulty_match = 1.0 - abs(prediction - target_difficulty)
                skill_predictions.append((skill, prediction, difficulty_match))
                
            except Exception as e:
                logger.warning(f"Error predicting for skill {skill}: {e}")
                continue
        
        # Sort by difficulty match (best match first)
        skill_predictions.sort(key=lambda x: x[2], reverse=True)
        
        # Return top recommendations
        recommendations = [(skill, pred) for skill, pred, _ in skill_predictions[:n_recommendations]]
        return recommendations


def run_prediction_examples():
    """Run example predictions to demonstrate the system"""
    print("="*60)
    print("KNOWLEDGE TRACING PREDICTION EXAMPLES")
    print("="*60)
    
    # Initialize predictor
    predictor = KTPredictor()
    
    # Load models
    print("\n1. Loading trained models...")
    load_status = predictor.load_models()
    
    if not any(load_status.values()):
        print("❌ No models could be loaded. Please train models first.")
        return
    
    print(f"✅ Loaded models: {[k for k, v in load_status.items() if v]}")
    
    # Example 1: Basic prediction
    print("\n2. Example 1: Basic Prediction")
    print("-" * 30)
    user_history = [1, 0, 1, 1]  # correct, incorrect, correct, correct
    skill = "Addition"
    
    try:
        # Get prediction from best available model
        available_models = [k for k, v in load_status.items() if v]
        model_type = 'ensemble' if 'ensemble' in available_models else available_models[0]
        
        prediction = predictor.predict_student_performance(
            user_history, skill, model_type
        )
        
        print(f"User history: {user_history}")
        print(f"Target skill: {skill}")
        print(f"Model used: {model_type}")
        print(f"Predicted probability of success: {prediction:.3f}")
        
    except Exception as e:
        print(f"Error in basic prediction: {e}")
    
    # Example 2: Model comparison
    print("\n3. Example 2: Model Comparison")
    print("-" * 30)
    
    try:
        predictions = predictor.compare_model_predictions(user_history, skill)
        
        print(f"Predictions for skill '{skill}' with history {user_history}:")
        for model, pred in predictions.items():
            print(f"  {model:12}: {pred:.3f}")
            
    except Exception as e:
        print(f"Error in model comparison: {e}")
    
    # Example 3: Learning trajectory
    print("\n4. Example 3: Learning Trajectory")
    print("-" * 30)
    
    try:
        trajectory = predictor.predict_learning_trajectory(
            user_history, skill, n_future_interactions=5
        )
        
        print(f"Predicted learning trajectory for '{skill}':")
        for i, prob in enumerate(trajectory, 1):
            print(f"  Interaction {i}: {prob:.3f}")
            
    except Exception as e:
        print(f"Error in trajectory prediction: {e}")
    
    # Example 4: Skill recommendations
    print("\n5. Example 4: Skill Recommendations")
    print("-" * 30)
    
    try:

        #load ../datasets/math/skills_all.csv
        skills_df = pd.read_csv("../datasets/math/skills_all.csv")
        available_skills = skills_df["skill_name"].tolist()
        
        recommendations = predictor.recommend_next_problems(
            user_history, available_skills, target_difficulty=0.7, n_recommendations=3
        )
        
        print(f"Recommended skills for target difficulty 0.7:")
        for i, (skill, prob) in enumerate(recommendations, 1):
            print(f"  {i}. {skill}: {prob:.3f} probability of success")
            
    except Exception as e:
        print(f"Error in skill recommendations: {e}")
    
    print("\n" + "="*60)
    print("PREDICTION EXAMPLES COMPLETED")
    print("="*60)


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Run examples
    run_prediction_examples()
