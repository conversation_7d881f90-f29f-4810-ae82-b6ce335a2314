{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f0a48d31-8eb5-4911-baf2-84c8a2764d7d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ All imports successful!\n", "Notebook started at: 2025-07-17 17:13:19.478925\n"]}], "source": ["import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "import json\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "# Add pipeline to path\n", "sys.path.append('/home/<USER>/workspace/AClass/App/Training/pipeline')\n", "\n", "\n", "\n", "print(\"✅ All imports successful!\")\n", "print(f\"Notebook started at: {datetime.now()}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "2d7d3782-ab92-4bbe-a216-05c345427b09", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading math dataset...\n", "✅ Dataset loaded successfully!\n", "Shape: (401756, 30)\n", "Columns: ['order_id', 'assignment_id', 'user_id', 'assistment_id', 'problem_id', 'original', 'correct', 'attempt_count', 'ms_first_response', 'tutor_mode', 'answer_type', 'sequence_id', 'student_class_id', 'position', 'type', 'base_sequence_id', 'skill_id', 'skill_name', 'teacher_id', 'school_id', 'hint_count', 'hint_total', 'overlap_time', 'template_id', 'answer_id', 'answer_text', 'first_action', 'bottom_hint', 'opportunity', 'opportunity_original']\n"]}], "source": ["# Load the math dataset\n", "data_path = \"/home/<USER>/workspace/AClass/App/Training/datasets/math/skill_builder_data_corrected.csv\"\n", "\n", "print(\"Loading math dataset...\")\n", "df= pd.read_csv(data_path, encoding='latin1')\n", "\n", "print(f\"✅ Dataset loaded successfully!\")\n", "print(f\"Shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")\n", "\n", "# Display basic info\n", "# df_clean.info()"]}, {"cell_type": "code", "execution_count": 3, "id": "fddd4be9-a7e2-455b-b6f5-2ef56067b6e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values in key columns:\n", "user_id: 0 (0.00%)\n", "problem_id: 0 (0.00%)\n", "skill_name: 76119 (18.95%)\n", "correct: 0 (0.00%)\n"]}], "source": ["# Check for missing values in key columns\n", "key_columns = ['user_id', 'problem_id', 'skill_name', 'correct']\n", "print(\"Missing values in key columns:\")\n", "for col in key_columns:\n", "    if col in df.columns:\n", "        missing = df[col].isnull().sum()\n", "        print(f\"{col}: {missing} ({missing/len(df)*100:.2f}%)\")\n", "    else:\n", "        print(f\"{col}: Column not found\")"]}, {"cell_type": "code", "execution_count": 4, "id": "0411ed18-a337-45c7-be25-f085fde33515", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cleaning dataset...\n", "Original size: 401,756\n", "Cleaned size: 325,637\n", "Removed: 76,119 rows (18.95%)\n"]}], "source": ["# Clean the dataset\n", "print(\"Cleaning dataset...\")\n", "original_size = len(df)\n", "\n", "# Remove rows with missing values in essential columns\n", "essential_cols = [col for col in key_columns if col in df.columns]\n", "df_clean = df.dropna(subset=essential_cols)\n", "\n", "# Remove duplicates\n", "df_clean = df_clean.drop_duplicates()\n", "\n", "# Ensure correct data types\n", "if 'correct' in df_clean.columns:\n", "    df_clean['correct'] = df_clean['correct'].astype(int)\n", "if 'user_id' in df_clean.columns:\n", "    df_clean['user_id'] = df_clean['user_id'].astype(str)\n", "if 'problem_id' in df_clean.columns:\n", "    df_clean['problem_id'] = df_clean['problem_id'].astype(str)\n", "\n", "print(f\"Original size: {original_size:,}\")\n", "print(f\"Cleaned size: {len(df_clean):,}\")\n", "print(f\"Removed: {original_size - len(df_clean):,} rows ({(original_size - len(df_clean))/original_size*100:.2f}%)\")"]}, {"cell_type": "code", "execution_count": 5, "id": "668d0a24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📋 COMPREHENSIVE SKILLS ANALYSIS\n", "==================================================\n", "Total number of unique skills: 110\n", "\n", "Top 15 skills by number of interactions:\n", "                                          interactions  accuracy  accuracy_std  unique_students  unique_problems\n", "skill_name                                                                                                      \n", "Equation Solving Two or Fewer Steps              24253     0.679         0.467              961             1040\n", "Conversion of Fraction Decimals Percents         18742     0.637         0.481             1225              488\n", "Addition and Subtraction Integers                12741     0.599         0.490             1226              413\n", "Addition and Subtraction Fractions               11334     0.677         0.468             1353              433\n", "Percent Of                                        9497     0.595         0.491             1115              465\n", "Proportion                                        9054     0.641         0.480              756              485\n", "Ordering Fractions                                8539     0.792         0.406              882              464\n", "Equation Solving More Than Two Steps              8115     0.758         0.428              412              419\n", "Probability of Two Distinct Events                7963     0.490         0.500              452              339\n", "Finding Percents                                  7694     0.538         0.499              771              371\n", "Subtraction Whole Numbers                         7669     0.641         0.480              903              242\n", "Probability of a Single Event                     7438     0.742         0.437              939              350\n", "Pattern Finding                                   7343     0.600         0.490              447              554\n", "Absolute Value                                    7340     0.757         0.429             1002              241\n", "Ordering Positive Decimals                        7317     0.750         0.433              942              543\n", "\n", "Top 15 easiest skills (highest accuracy):\n", "                                              interactions  accuracy  unique_students\n", "skill_name                                                                           \n", "Nets of 3D Figures                                     280     0.950              229\n", "Area Parallelogram                                     115     0.922               95\n", "Congruence                                             587     0.894              364\n", "Distributive Property                                   18     0.889                5\n", "Mode                                                  1926     0.876              572\n", "Scatter Plot                                          1859     0.869              354\n", "Area Rectangle                                         495     0.863              215\n", "Area Triangle                                          286     0.857              168\n", "D.4.8-understanding-concept-of-probabilities           456     0.846              202\n", "Volume Rectangular Prism                               926     0.840              345\n", "Fraction Of                                            607     0.830              288\n", "Write Linear Equation from Situation                  1447     0.822              223\n", "Linear Equations                                        89     0.820               41\n", "Slope                                                   89     0.820               41\n", "Choose an Equation from Given Information               89     0.820               41\n", "\n", "Top 15 hardest skills (lowest accuracy):\n", "                                               interactions  accuracy  unique_students\n", "skill_name                                                                            \n", "Reading a Ruler or Scale                                  5     0.000                5\n", "Quadratic Formula to Solve Quadratic Equation            32     0.125               14\n", "Rotations                                               427     0.136              163\n", "Computation with Real Numbers                            21     0.190               21\n", "Solving Systems of Linear Equations                     234     0.192               22\n", "Percent Discount                                         47     0.234               29\n", "Surface Area Cylinder                                   491     0.316              135\n", "Finding Slope From Situation                              9     0.333                2\n", "Percents                                                117     0.333               41\n", "Algebraic Solving                                       389     0.368               88\n", "Reflection                                              459     0.373              176\n", "Rate                                                     91     0.374               39\n", "Algebraic Simplification                                 90     0.400               15\n", "Finding <PERSON><PERSON><PERSON> from Ordered Pairs                          5     0.400                2\n", "Multiplication Whole Numbers                            110     0.436               45\n"]}], "source": ["# Comprehensive skills analysis\n", "print(\"📋 COMPREHENSIVE SKILLS ANALYSIS\")\n", "print(\"=\"*50)\n", "\n", "# Get all unique skills\n", "all_skills = df_clean['skill_name'].unique()\n", "print(f\"Total number of unique skills: {len(all_skills)}\")\n", "\n", "# Skills statistics\n", "skills_stats = df_clean.groupby('skill_name').agg({\n", "    'correct': ['count', 'mean', 'std'],\n", "    'user_id': 'nunique',\n", "    'problem_id': 'nunique'\n", "}).round(3)\n", "\n", "skills_stats.columns = ['interactions', 'accuracy', 'accuracy_std', 'unique_students', 'unique_problems']\n", "skills_stats = skills_stats.sort_values('interactions', ascending=False)\n", "\n", "print(\"\\nTop 15 skills by number of interactions:\")\n", "print(skills_stats.head(15).to_string())\n", "\n", "print(\"\\nTop 15 easiest skills (highest accuracy):\")\n", "easiest_skills = skills_stats.sort_values('accuracy', ascending=False).head(15)\n", "print(easiest_skills[['interactions', 'accuracy', 'unique_students']].to_string())\n", "\n", "print(\"\\nTop 15 hardest skills (lowest accuracy):\")\n", "hardest_skills = skills_stats.sort_values('accuracy', ascending=True).head(15)\n", "print(hardest_skills[['interactions', 'accuracy', 'unique_students']].to_string())"]}, {"cell_type": "code", "execution_count": 6, "id": "0cedca97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Splitting data into train/test sets...\n", "Training set: 255,487 interactions from 3,320 users\n", "Test set: 70,150 interactions from 831 users\n", "Train accuracy: 0.655\n", "Test accuracy: 0.670\n"]}], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "# Split data by users to avoid data leakage\n", "print(\"Splitting data into train/test sets...\")\n", "\n", "# df_clean = df_clean.head(50000)\n", "# Get unique users\n", "unique_users = df_clean['user_id'].unique()\n", "train_users, test_users = train_test_split(unique_users, test_size=0.2, random_state=42)\n", "\n", "# Split data based on users\n", "train_df = df_clean[df_clean['user_id'].isin(train_users)].copy()\n", "test_df = df_clean[df_clean['user_id'].isin(test_users)].copy()\n", "\n", "\n", "print(f\"Training set: {len(train_df):,} interactions from {len(train_users):,} users\")\n", "print(f\"Test set: {len(test_df):,} interactions from {len(test_users):,} users\")\n", "print(f\"Train accuracy: {train_df['correct'].mean():.3f}\")\n", "print(f\"Test accuracy: {test_df['correct'].mean():.3f}\")"]}, {"cell_type": "code", "execution_count": 7, "id": "b6c9773e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-17 17:13:25.038692: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2025-07-17 17:13:27,232 - INFO - Exploring dataset...\n", "2025-07-17 17:13:27,298 - INFO - Dataset exploration completed\n", "2025-07-17 17:13:27,299 - INFO -   total_interactions: 401756\n", "2025-07-17 17:13:27,300 - INFO -   unique_students: 4217\n", "2025-07-17 17:13:27,301 - INFO -   unique_problems: 26688\n", "2025-07-17 17:13:27,302 - INFO -   unique_skills: 110\n", "2025-07-17 17:13:27,303 - INFO -   overall_accuracy: 0.6429225699180597\n", "2025-07-17 17:13:27,304 - INFO -   correct_responses: 258298\n", "2025-07-17 17:13:27,305 - INFO -   incorrect_responses: 143458\n", "2025-07-17 17:13:27,305 - INFO -   avg_sequence_length: 95.27057149632441\n", "2025-07-17 17:13:27,306 - INFO -   max_sequence_length: 1606\n", "2025-07-17 17:13:27,307 - INFO -   min_sequence_length: 1\n", "2025-07-17 17:13:27,308 - INFO -   most_common_skill: Equation Solving Two or Fewer Steps\n", "2025-07-17 17:13:27,309 - INFO -   most_common_skill_count: 24253\n", "2025-07-17 17:13:27,310 - INFO -   least_common_skill: Finding Slope from Ordered Pairs\n", "2025-07-17 17:13:27,311 - INFO -   least_common_skill_count: 5\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🚀 INITIALIZING <PERSON><PERSON><PERSON>LEDGE TRACING PIPELINE\n", "==================================================\n"]}], "source": ["# from kt_model_trainer import KTModelTrainer\n", "# from train_pfa import PFAKnowledgeTracing\n", "# # Initialize trainer with custom settings\n", "# trainer = KTModelTrainer(n_folds=10, random_state=42)\n", "\n", "# Import pipeline components\n", "from kt_training_pipeline import KnowledgeTracingPipeline\n", "from kt_evaluation import KTEvaluator\n", "from prediction import KTPredictor\n", "from train_pfa import PFAKnowledgeTracing\n", "\n", "# Initialize the knowledge tracing pipeline\n", "print(\"🚀 INITIALIZING K<PERSON><PERSON>LEDGE TRACING PIPELINE\")\n", "print(\"=\"*50)\n", "\n", "pipeline = KnowledgeTracingPipeline()\n", "\n", "dataset_stats = pipeline.explore_dataset(df)\n", "\n", "\n", "pipeline.datasets = {\n", "    'raw': df,\n", "    'clean': df_clean,\n", "    'train': train_df,\n", "    'test': test_df,\n", "    'stats': skills_stats\n", "}\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "id": "d336cbec", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-17 17:13:28,131 - INFO - Training knowledge tracing models...\n", "2025-07-17 17:13:28,133 - INFO - Training PFA model... AG\n", "2025-07-17 17:13:28,134 - INFO - Training PFA model...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["START PFA MODIFIED : AG\n", "Total unique skills: 111\n", "Creating 111 skill columns\n", "Creating PFA features...\n", "Feature matrix shape: (325637, 118)\n", "Feature columns: ['success_count', 'failure_count', 'total_attempts', 'attempt_count', 'hint_count', 'ms_first_response', 'opportunity', 'skill_1.0', 'skill_2.0', 'skill_4.0', 'skill_5.0', 'skill_8.0', 'skill_9.0', 'skill_10.0', 'skill_11.0', 'skill_12.0', 'skill_13.0', 'skill_14.0', 'skill_15.0', 'skill_16.0', 'skill_17.0', 'skill_18.0', 'skill_21.0', 'skill_22.0', 'skill_24.0', 'skill_25.0', 'skill_26.0', 'skill_27.0', 'skill_32.0', 'skill_34.0', 'skill_35.0', 'skill_39.0', 'skill_40.0', 'skill_42.0', 'skill_43.0', 'skill_46.0', 'skill_47.0', 'skill_48.0', 'skill_49.0', 'skill_50.0', 'skill_51.0', 'skill_53.0', 'skill_54.0', 'skill_58.0', 'skill_61.0', 'skill_63.0', 'skill_64.0', 'skill_65.0', 'skill_67.0', 'skill_69.0', 'skill_70.0', 'skill_74.0', 'skill_75.0', 'skill_77.0', 'skill_79.0', 'skill_80.0', 'skill_81.0', 'skill_82.0', 'skill_83.0', 'skill_84.0', 'skill_85.0', 'skill_86.0', 'skill_92.0', 'skill_110.0', 'skill_163.0', 'skill_165.0', 'skill_166.0', 'skill_173.0', 'skill_190.0', 'skill_193.0', 'skill_203.0', 'skill_204.0', 'skill_217.0', 'skill_221.0', 'skill_276.0', 'skill_277.0', 'skill_278.0', 'skill_279.0', 'skill_280.0', 'skill_290.0', 'skill_292.0', 'skill_293.0', 'skill_294.0', 'skill_295.0', 'skill_296.0', 'skill_297.0', 'skill_298.0', 'skill_299.0', 'skill_301.0', 'skill_303.0', 'skill_307.0', 'skill_308.0', 'skill_309.0', 'skill_310.0', 'skill_311.0', 'skill_312.0', 'skill_314.0', 'skill_317.0', 'skill_321.0', 'skill_322.0', 'skill_323.0', 'skill_324.0', 'skill_325.0', 'skill_331.0', 'skill_333.0', 'skill_334.0', 'skill_340.0', 'skill_343.0', 'skill_346.0', 'skill_348.0', 'skill_350.0', 'skill_356.0', 'skill_362.0', 'skill_365.0', 'skill_368.0', 'skill_371.0', 'skill_375.0', 'skill_378.0']\n", "\n", "Splitting data for train/test...\n", "Training set size: 260509\n", "Test set size: 65128\n", "Training features: 118\n", "Test features: 118\n", "✓ Train and test have identical feature columns\n", "Training PFA model...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-17 17:19:44,165 - INFO - PFA training completed\n", "2025-07-17 17:19:44,190 - INFO -   PFA metrics: {'accuracy': 0.8617031077263235, 'precision': 0.835037680242103, 'recall': 0.9844464135808226, 'f1': 0.9036076240622425, 'auc': 0.8765512237728135}\n", "2025-07-17 17:19:44,190 - INFO -   PFA model: <train_pfa.PFAKnowledgeTracing object at 0x77c85c044460>\n", "2025-07-17 17:19:44,191 - INFO -   PFA model skills: {'1.0': {'coefficient': 0.09683778943767787, 'difficulty': -0.09683778943767787, 'frequency': 0}, '2.0': {'coefficient': -0.03308453785700859, 'difficulty': 0.03308453785700859, 'frequency': 0}, '4.0': {'coefficient': 0.04729962025729832, 'difficulty': -0.04729962025729832, 'frequency': 0}, '5.0': {'coefficient': 0.01962207773044843, 'difficulty': -0.01962207773044843, 'frequency': 0}, '8.0': {'coefficient': 0.09373956665435047, 'difficulty': -0.09373956665435047, 'frequency': 0}, '9.0': {'coefficient': -0.014032475233269592, 'difficulty': 0.014032475233269592, 'frequency': 0}, '10.0': {'coefficient': -0.07789486543201653, 'difficulty': 0.07789486543201653, 'frequency': 0}, '11.0': {'coefficient': -0.06705070731722433, 'difficulty': 0.06705070731722433, 'frequency': 0}, '12.0': {'coefficient': -0.08136211605049135, 'difficulty': 0.08136211605049135, 'frequency': 0}, '13.0': {'coefficient': -0.005747100695111879, 'difficulty': 0.005747100695111879, 'frequency': 0}, '14.0': {'coefficient': 0.05140853189743306, 'difficulty': -0.05140853189743306, 'frequency': 0}, '15.0': {'coefficient': 0.04649145738600379, 'difficulty': -0.04649145738600379, 'frequency': 0}, '16.0': {'coefficient': -0.17684272957250646, 'difficulty': 0.17684272957250646, 'frequency': 0}, '17.0': {'coefficient': -0.17191522059527173, 'difficulty': 0.17191522059527173, 'frequency': 0}, '18.0': {'coefficient': 0.027496150002431656, 'difficulty': -0.027496150002431656, 'frequency': 0}, '21.0': {'coefficient': -0.04214002849890023, 'difficulty': 0.04214002849890023, 'frequency': 0}, '22.0': {'coefficient': -0.04992957142930989, 'difficulty': 0.04992957142930989, 'frequency': 0}, '24.0': {'coefficient': 0.07370943635765251, 'difficulty': -0.07370943635765251, 'frequency': 0}, '25.0': {'coefficient': 0.07453329278520444, 'difficulty': -0.07453329278520444, 'frequency': 0}, '26.0': {'coefficient': 0.018667234192024314, 'difficulty': -0.018667234192024314, 'frequency': 0}, '27.0': {'coefficient': -0.07153466462614105, 'difficulty': 0.07153466462614105, 'frequency': 0}, '32.0': {'coefficient': 0.05864340805823193, 'difficulty': -0.05864340805823193, 'frequency': 0}, '34.0': {'coefficient': -0.07015577170482415, 'difficulty': 0.07015577170482415, 'frequency': 0}, '35.0': {'coefficient': 0.057479087772037314, 'difficulty': -0.057479087772037314, 'frequency': 0}, '39.0': {'coefficient': 0.04524544049249672, 'difficulty': -0.04524544049249672, 'frequency': 0}, '40.0': {'coefficient': 0.0662741288223963, 'difficulty': -0.0662741288223963, 'frequency': 0}, '42.0': {'coefficient': 0.01646754931114342, 'difficulty': -0.01646754931114342, 'frequency': 0}, '43.0': {'coefficient': -0.06611428689965704, 'difficulty': 0.06611428689965704, 'frequency': 0}, '46.0': {'coefficient': 0.09346491188613062, 'difficulty': -0.09346491188613062, 'frequency': 0}, '47.0': {'coefficient': 0.20545713000117383, 'difficulty': -0.20545713000117383, 'frequency': 0}, '48.0': {'coefficient': -0.06205413396236038, 'difficulty': 0.06205413396236038, 'frequency': 0}, '49.0': {'coefficient': 0.007631456098138198, 'difficulty': -0.007631456098138198, 'frequency': 0}, '50.0': {'coefficient': -0.04275593890782636, 'difficulty': 0.04275593890782636, 'frequency': 0}, '51.0': {'coefficient': 0.06586756624209354, 'difficulty': -0.06586756624209354, 'frequency': 0}, '53.0': {'coefficient': 0.021843889590217432, 'difficulty': -0.021843889590217432, 'frequency': 0}, '54.0': {'coefficient': 0.039665855347272384, 'difficulty': -0.039665855347272384, 'frequency': 0}, '58.0': {'coefficient': -0.0027366760497599192, 'difficulty': 0.0027366760497599192, 'frequency': 0}, '61.0': {'coefficient': 0.11098280843120378, 'difficulty': -0.11098280843120378, 'frequency': 0}, '63.0': {'coefficient': -0.026086479194641396, 'difficulty': 0.026086479194641396, 'frequency': 0}, '64.0': {'coefficient': -0.0005276796250007275, 'difficulty': 0.0005276796250007275, 'frequency': 0}, '65.0': {'coefficient': 0.0795686814007052, 'difficulty': -0.0795686814007052, 'frequency': 0}, '67.0': {'coefficient': 0.1093669970369555, 'difficulty': -0.1093669970369555, 'frequency': 0}, '69.0': {'coefficient': 0.009531090163547052, 'difficulty': -0.009531090163547052, 'frequency': 0}, '70.0': {'coefficient': -0.002799514173071238, 'difficulty': 0.002799514173071238, 'frequency': 0}, '74.0': {'coefficient': -0.11449746024476912, 'difficulty': 0.11449746024476912, 'frequency': 0}, '75.0': {'coefficient': -0.020873737536932047, 'difficulty': 0.020873737536932047, 'frequency': 0}, '77.0': {'coefficient': -0.1365992788659508, 'difficulty': 0.1365992788659508, 'frequency': 0}, '79.0': {'coefficient': 0.004775289083400196, 'difficulty': -0.004775289083400196, 'frequency': 0}, '80.0': {'coefficient': 0.027850267327958485, 'difficulty': -0.027850267327958485, 'frequency': 0}, '81.0': {'coefficient': -0.1590943646317561, 'difficulty': 0.1590943646317561, 'frequency': 0}, '82.0': {'coefficient': 0.07433170194898246, 'difficulty': -0.07433170194898246, 'frequency': 0}, '83.0': {'coefficient': 0.0559285608538576, 'difficulty': -0.0559285608538576, 'frequency': 0}, '84.0': {'coefficient': 0.03872029223586921, 'difficulty': -0.03872029223586921, 'frequency': 0}, '85.0': {'coefficient': -0.06623530804111882, 'difficulty': 0.06623530804111882, 'frequency': 0}, '86.0': {'coefficient': 0.07082597439071722, 'difficulty': -0.07082597439071722, 'frequency': 0}, '92.0': {'coefficient': -0.06588776092093405, 'difficulty': 0.06588776092093405, 'frequency': 0}, '110.0': {'coefficient': 0.04910491072736027, 'difficulty': -0.04910491072736027, 'frequency': 0}, '163.0': {'coefficient': 0.016182528255127355, 'difficulty': -0.016182528255127355, 'frequency': 0}, '165.0': {'coefficient': 0.01688879854653848, 'difficulty': -0.01688879854653848, 'frequency': 0}, '166.0': {'coefficient': 0.025277550099454163, 'difficulty': -0.025277550099454163, 'frequency': 0}, '173.0': {'coefficient': 0.016721285054957673, 'difficulty': -0.016721285054957673, 'frequency': 0}, '190.0': {'coefficient': 0.014593645197055704, 'difficulty': -0.014593645197055704, 'frequency': 0}, '193.0': {'coefficient': 0.013548432501832408, 'difficulty': -0.013548432501832408, 'frequency': 0}, '203.0': {'coefficient': -0.03374441157368696, 'difficulty': 0.03374441157368696, 'frequency': 0}, '204.0': {'coefficient': -0.04400737296374124, 'difficulty': 0.04400737296374124, 'frequency': 0}, '217.0': {'coefficient': -0.04100699334002504, 'difficulty': 0.04100699334002504, 'frequency': 0}, '221.0': {'coefficient': 0.014068317534014572, 'difficulty': -0.014068317534014572, 'frequency': 0}, '276.0': {'coefficient': 0.07173387734965538, 'difficulty': -0.07173387734965538, 'frequency': 0}, '277.0': {'coefficient': 0.0923882338922502, 'difficulty': -0.0923882338922502, 'frequency': 0}, '278.0': {'coefficient': -0.1621192469312278, 'difficulty': 0.1621192469312278, 'frequency': 0}, '279.0': {'coefficient': -0.004825744046697401, 'difficulty': 0.004825744046697401, 'frequency': 0}, '280.0': {'coefficient': 0.16684872959683122, 'difficulty': -0.16684872959683122, 'frequency': 0}, '290.0': {'coefficient': 0.015933610956848712, 'difficulty': -0.015933610956848712, 'frequency': 0}, '292.0': {'coefficient': 0.0037521419053216116, 'difficulty': -0.0037521419053216116, 'frequency': 0}, '293.0': {'coefficient': 0.022226068669105484, 'difficulty': -0.022226068669105484, 'frequency': 0}, '294.0': {'coefficient': -0.04013082283279721, 'difficulty': 0.04013082283279721, 'frequency': 0}, '295.0': {'coefficient': 0.0393337131531375, 'difficulty': -0.0393337131531375, 'frequency': 0}, '296.0': {'coefficient': 0.03544391758552733, 'difficulty': -0.03544391758552733, 'frequency': 0}, '297.0': {'coefficient': -0.00014142465276447428, 'difficulty': 0.00014142465276447428, 'frequency': 0}, '298.0': {'coefficient': 0.03213886840671009, 'difficulty': -0.03213886840671009, 'frequency': 0}, '299.0': {'coefficient': 0.027774772423323657, 'difficulty': -0.027774772423323657, 'frequency': 0}, '301.0': {'coefficient': 0.06054202496758038, 'difficulty': -0.06054202496758038, 'frequency': 0}, '303.0': {'coefficient': 0.027459951469849826, 'difficulty': -0.027459951469849826, 'frequency': 0}, '307.0': {'coefficient': 0.07843981863917156, 'difficulty': -0.07843981863917156, 'frequency': 0}, '308.0': {'coefficient': -0.034370981778936306, 'difficulty': 0.034370981778936306, 'frequency': 0}, '309.0': {'coefficient': -0.02108820889707049, 'difficulty': 0.02108820889707049, 'frequency': 0}, '310.0': {'coefficient': -0.012531876246382325, 'difficulty': 0.012531876246382325, 'frequency': 0}, '311.0': {'coefficient': -0.07834717272102079, 'difficulty': 0.07834717272102079, 'frequency': 0}, '312.0': {'coefficient': -0.09034858909527375, 'difficulty': 0.09034858909527375, 'frequency': 0}, '314.0': {'coefficient': -0.01101840801242009, 'difficulty': 0.01101840801242009, 'frequency': 0}, '317.0': {'coefficient': 0.030324769750500555, 'difficulty': -0.030324769750500555, 'frequency': 0}, '321.0': {'coefficient': -0.02106316632441417, 'difficulty': 0.02106316632441417, 'frequency': 0}, '322.0': {'coefficient': 0.03793416283210171, 'difficulty': -0.03793416283210171, 'frequency': 0}, '323.0': {'coefficient': 0.02971580280589126, 'difficulty': -0.02971580280589126, 'frequency': 0}, '324.0': {'coefficient': -0.0016198381246118466, 'difficulty': 0.0016198381246118466, 'frequency': 0}, '325.0': {'coefficient': -0.08999619378908587, 'difficulty': 0.08999619378908587, 'frequency': 0}, '331.0': {'coefficient': 0.01899989177655631, 'difficulty': -0.01899989177655631, 'frequency': 0}, '333.0': {'coefficient': 0.00021337178893276242, 'difficulty': -0.00021337178893276242, 'frequency': 0}, '334.0': {'coefficient': -0.00020674589929668433, 'difficulty': 0.00020674589929668433, 'frequency': 0}, '340.0': {'coefficient': 0.005512155938894222, 'difficulty': -0.005512155938894222, 'frequency': 0}, '343.0': {'coefficient': 0.007972608046860365, 'difficulty': -0.007972608046860365, 'frequency': 0}, '346.0': {'coefficient': 0.002108318094966745, 'difficulty': -0.002108318094966745, 'frequency': 0}, '348.0': {'coefficient': -0.0015571053730574027, 'difficulty': 0.0015571053730574027, 'frequency': 0}, '350.0': {'coefficient': 0.013073016530757028, 'difficulty': -0.013073016530757028, 'frequency': 0}, '356.0': {'coefficient': -0.0015220999933770818, 'difficulty': 0.0015220999933770818, 'frequency': 0}, '362.0': {'coefficient': -0.010163686798467733, 'difficulty': 0.010163686798467733, 'frequency': 0}, '365.0': {'coefficient': 0.0043361735034565954, 'difficulty': -0.0043361735034565954, 'frequency': 0}, '368.0': {'coefficient': 0.07336515061030588, 'difficulty': -0.07336515061030588, 'frequency': 0}, '371.0': {'coefficient': 0.0054860222522636485, 'difficulty': -0.0054860222522636485, 'frequency': 0}, '375.0': {'coefficient': 0.021728049845083974, 'difficulty': -0.021728049845083974, 'frequency': 0}, '378.0': {'coefficient': 0.03518385878110647, 'difficulty': -0.03518385878110647, 'frequency': 0}}\n", "2025-07-17 17:19:44,193 - INFO - Model training completed\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Model training completed!\n", "✅ Individual models training completed\n"]}], "source": ["models, metrics = pipeline.train_models(train_df, test_df, models_to_train=['pfa'])\n", "print(\"✅ Individual models training completed\")\n", "    "]}, {"cell_type": "code", "execution_count": 9, "id": "133af204", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'1.0': {'coefficient': 0.09683778943767787,\n", "  'difficulty': -0.09683778943767787,\n", "  'frequency': 0},\n", " '2.0': {'coefficient': -0.03308453785700859,\n", "  'difficulty': 0.03308453785700859,\n", "  'frequency': 0},\n", " '4.0': {'coefficient': 0.04729962025729832,\n", "  'difficulty': -0.04729962025729832,\n", "  'frequency': 0},\n", " '5.0': {'coefficient': 0.01962207773044843,\n", "  'difficulty': -0.01962207773044843,\n", "  'frequency': 0},\n", " '8.0': {'coefficient': 0.09373956665435047,\n", "  'difficulty': -0.09373956665435047,\n", "  'frequency': 0},\n", " '9.0': {'coefficient': -0.014032475233269592,\n", "  'difficulty': 0.014032475233269592,\n", "  'frequency': 0},\n", " '10.0': {'coefficient': -0.07789486543201653,\n", "  'difficulty': 0.07789486543201653,\n", "  'frequency': 0},\n", " '11.0': {'coefficient': -0.06705070731722433,\n", "  'difficulty': 0.06705070731722433,\n", "  'frequency': 0},\n", " '12.0': {'coefficient': -0.08136211605049135,\n", "  'difficulty': 0.08136211605049135,\n", "  'frequency': 0},\n", " '13.0': {'coefficient': -0.005747100695111879,\n", "  'difficulty': 0.005747100695111879,\n", "  'frequency': 0},\n", " '14.0': {'coefficient': 0.05140853189743306,\n", "  'difficulty': -0.05140853189743306,\n", "  'frequency': 0},\n", " '15.0': {'coefficient': 0.04649145738600379,\n", "  'difficulty': -0.04649145738600379,\n", "  'frequency': 0},\n", " '16.0': {'coefficient': -0.17684272957250646,\n", "  'difficulty': 0.17684272957250646,\n", "  'frequency': 0},\n", " '17.0': {'coefficient': -0.17191522059527173,\n", "  'difficulty': 0.17191522059527173,\n", "  'frequency': 0},\n", " '18.0': {'coefficient': 0.027496150002431656,\n", "  'difficulty': -0.027496150002431656,\n", "  'frequency': 0},\n", " '21.0': {'coefficient': -0.04214002849890023,\n", "  'difficulty': 0.04214002849890023,\n", "  'frequency': 0},\n", " '22.0': {'coefficient': -0.04992957142930989,\n", "  'difficulty': 0.04992957142930989,\n", "  'frequency': 0},\n", " '24.0': {'coefficient': 0.07370943635765251,\n", "  'difficulty': -0.07370943635765251,\n", "  'frequency': 0},\n", " '25.0': {'coefficient': 0.07453329278520444,\n", "  'difficulty': -0.07453329278520444,\n", "  'frequency': 0},\n", " '26.0': {'coefficient': 0.018667234192024314,\n", "  'difficulty': -0.018667234192024314,\n", "  'frequency': 0},\n", " '27.0': {'coefficient': -0.07153466462614105,\n", "  'difficulty': 0.07153466462614105,\n", "  'frequency': 0},\n", " '32.0': {'coefficient': 0.05864340805823193,\n", "  'difficulty': -0.05864340805823193,\n", "  'frequency': 0},\n", " '34.0': {'coefficient': -0.07015577170482415,\n", "  'difficulty': 0.07015577170482415,\n", "  'frequency': 0},\n", " '35.0': {'coefficient': 0.057479087772037314,\n", "  'difficulty': -0.057479087772037314,\n", "  'frequency': 0},\n", " '39.0': {'coefficient': 0.04524544049249672,\n", "  'difficulty': -0.04524544049249672,\n", "  'frequency': 0},\n", " '40.0': {'coefficient': 0.0662741288223963,\n", "  'difficulty': -0.0662741288223963,\n", "  'frequency': 0},\n", " '42.0': {'coefficient': 0.01646754931114342,\n", "  'difficulty': -0.01646754931114342,\n", "  'frequency': 0},\n", " '43.0': {'coefficient': -0.06611428689965704,\n", "  'difficulty': 0.06611428689965704,\n", "  'frequency': 0},\n", " '46.0': {'coefficient': 0.09346491188613062,\n", "  'difficulty': -0.09346491188613062,\n", "  'frequency': 0},\n", " '47.0': {'coefficient': 0.20545713000117383,\n", "  'difficulty': -0.20545713000117383,\n", "  'frequency': 0},\n", " '48.0': {'coefficient': -0.06205413396236038,\n", "  'difficulty': 0.06205413396236038,\n", "  'frequency': 0},\n", " '49.0': {'coefficient': 0.007631456098138198,\n", "  'difficulty': -0.007631456098138198,\n", "  'frequency': 0},\n", " '50.0': {'coefficient': -0.04275593890782636,\n", "  'difficulty': 0.04275593890782636,\n", "  'frequency': 0},\n", " '51.0': {'coefficient': 0.06586756624209354,\n", "  'difficulty': -0.06586756624209354,\n", "  'frequency': 0},\n", " '53.0': {'coefficient': 0.021843889590217432,\n", "  'difficulty': -0.021843889590217432,\n", "  'frequency': 0},\n", " '54.0': {'coefficient': 0.039665855347272384,\n", "  'difficulty': -0.039665855347272384,\n", "  'frequency': 0},\n", " '58.0': {'coefficient': -0.0027366760497599192,\n", "  'difficulty': 0.0027366760497599192,\n", "  'frequency': 0},\n", " '61.0': {'coefficient': 0.11098280843120378,\n", "  'difficulty': -0.11098280843120378,\n", "  'frequency': 0},\n", " '63.0': {'coefficient': -0.026086479194641396,\n", "  'difficulty': 0.026086479194641396,\n", "  'frequency': 0},\n", " '64.0': {'coefficient': -0.0005276796250007275,\n", "  'difficulty': 0.0005276796250007275,\n", "  'frequency': 0},\n", " '65.0': {'coefficient': 0.0795686814007052,\n", "  'difficulty': -0.0795686814007052,\n", "  'frequency': 0},\n", " '67.0': {'coefficient': 0.1093669970369555,\n", "  'difficulty': -0.1093669970369555,\n", "  'frequency': 0},\n", " '69.0': {'coefficient': 0.009531090163547052,\n", "  'difficulty': -0.009531090163547052,\n", "  'frequency': 0},\n", " '70.0': {'coefficient': -0.002799514173071238,\n", "  'difficulty': 0.002799514173071238,\n", "  'frequency': 0},\n", " '74.0': {'coefficient': -0.11449746024476912,\n", "  'difficulty': 0.11449746024476912,\n", "  'frequency': 0},\n", " '75.0': {'coefficient': -0.020873737536932047,\n", "  'difficulty': 0.020873737536932047,\n", "  'frequency': 0},\n", " '77.0': {'coefficient': -0.1365992788659508,\n", "  'difficulty': 0.1365992788659508,\n", "  'frequency': 0},\n", " '79.0': {'coefficient': 0.004775289083400196,\n", "  'difficulty': -0.004775289083400196,\n", "  'frequency': 0},\n", " '80.0': {'coefficient': 0.027850267327958485,\n", "  'difficulty': -0.027850267327958485,\n", "  'frequency': 0},\n", " '81.0': {'coefficient': -0.1590943646317561,\n", "  'difficulty': 0.1590943646317561,\n", "  'frequency': 0},\n", " '82.0': {'coefficient': 0.07433170194898246,\n", "  'difficulty': -0.07433170194898246,\n", "  'frequency': 0},\n", " '83.0': {'coefficient': 0.0559285608538576,\n", "  'difficulty': -0.0559285608538576,\n", "  'frequency': 0},\n", " '84.0': {'coefficient': 0.03872029223586921,\n", "  'difficulty': -0.03872029223586921,\n", "  'frequency': 0},\n", " '85.0': {'coefficient': -0.06623530804111882,\n", "  'difficulty': 0.06623530804111882,\n", "  'frequency': 0},\n", " '86.0': {'coefficient': 0.07082597439071722,\n", "  'difficulty': -0.07082597439071722,\n", "  'frequency': 0},\n", " '92.0': {'coefficient': -0.06588776092093405,\n", "  'difficulty': 0.06588776092093405,\n", "  'frequency': 0},\n", " '110.0': {'coefficient': 0.04910491072736027,\n", "  'difficulty': -0.04910491072736027,\n", "  'frequency': 0},\n", " '163.0': {'coefficient': 0.016182528255127355,\n", "  'difficulty': -0.016182528255127355,\n", "  'frequency': 0},\n", " '165.0': {'coefficient': 0.01688879854653848,\n", "  'difficulty': -0.01688879854653848,\n", "  'frequency': 0},\n", " '166.0': {'coefficient': 0.025277550099454163,\n", "  'difficulty': -0.025277550099454163,\n", "  'frequency': 0},\n", " '173.0': {'coefficient': 0.016721285054957673,\n", "  'difficulty': -0.016721285054957673,\n", "  'frequency': 0},\n", " '190.0': {'coefficient': 0.014593645197055704,\n", "  'difficulty': -0.014593645197055704,\n", "  'frequency': 0},\n", " '193.0': {'coefficient': 0.013548432501832408,\n", "  'difficulty': -0.013548432501832408,\n", "  'frequency': 0},\n", " '203.0': {'coefficient': -0.03374441157368696,\n", "  'difficulty': 0.03374441157368696,\n", "  'frequency': 0},\n", " '204.0': {'coefficient': -0.04400737296374124,\n", "  'difficulty': 0.04400737296374124,\n", "  'frequency': 0},\n", " '217.0': {'coefficient': -0.04100699334002504,\n", "  'difficulty': 0.04100699334002504,\n", "  'frequency': 0},\n", " '221.0': {'coefficient': 0.014068317534014572,\n", "  'difficulty': -0.014068317534014572,\n", "  'frequency': 0},\n", " '276.0': {'coefficient': 0.07173387734965538,\n", "  'difficulty': -0.07173387734965538,\n", "  'frequency': 0},\n", " '277.0': {'coefficient': 0.0923882338922502,\n", "  'difficulty': -0.0923882338922502,\n", "  'frequency': 0},\n", " '278.0': {'coefficient': -0.1621192469312278,\n", "  'difficulty': 0.1621192469312278,\n", "  'frequency': 0},\n", " '279.0': {'coefficient': -0.004825744046697401,\n", "  'difficulty': 0.004825744046697401,\n", "  'frequency': 0},\n", " '280.0': {'coefficient': 0.16684872959683122,\n", "  'difficulty': -0.16684872959683122,\n", "  'frequency': 0},\n", " '290.0': {'coefficient': 0.015933610956848712,\n", "  'difficulty': -0.015933610956848712,\n", "  'frequency': 0},\n", " '292.0': {'coefficient': 0.0037521419053216116,\n", "  'difficulty': -0.0037521419053216116,\n", "  'frequency': 0},\n", " '293.0': {'coefficient': 0.022226068669105484,\n", "  'difficulty': -0.022226068669105484,\n", "  'frequency': 0},\n", " '294.0': {'coefficient': -0.04013082283279721,\n", "  'difficulty': 0.04013082283279721,\n", "  'frequency': 0},\n", " '295.0': {'coefficient': 0.0393337131531375,\n", "  'difficulty': -0.0393337131531375,\n", "  'frequency': 0},\n", " '296.0': {'coefficient': 0.03544391758552733,\n", "  'difficulty': -0.03544391758552733,\n", "  'frequency': 0},\n", " '297.0': {'coefficient': -0.00014142465276447428,\n", "  'difficulty': 0.00014142465276447428,\n", "  'frequency': 0},\n", " '298.0': {'coefficient': 0.03213886840671009,\n", "  'difficulty': -0.03213886840671009,\n", "  'frequency': 0},\n", " '299.0': {'coefficient': 0.027774772423323657,\n", "  'difficulty': -0.027774772423323657,\n", "  'frequency': 0},\n", " '301.0': {'coefficient': 0.06054202496758038,\n", "  'difficulty': -0.06054202496758038,\n", "  'frequency': 0},\n", " '303.0': {'coefficient': 0.027459951469849826,\n", "  'difficulty': -0.027459951469849826,\n", "  'frequency': 0},\n", " '307.0': {'coefficient': 0.07843981863917156,\n", "  'difficulty': -0.07843981863917156,\n", "  'frequency': 0},\n", " '308.0': {'coefficient': -0.034370981778936306,\n", "  'difficulty': 0.034370981778936306,\n", "  'frequency': 0},\n", " '309.0': {'coefficient': -0.02108820889707049,\n", "  'difficulty': 0.02108820889707049,\n", "  'frequency': 0},\n", " '310.0': {'coefficient': -0.012531876246382325,\n", "  'difficulty': 0.012531876246382325,\n", "  'frequency': 0},\n", " '311.0': {'coefficient': -0.07834717272102079,\n", "  'difficulty': 0.07834717272102079,\n", "  'frequency': 0},\n", " '312.0': {'coefficient': -0.09034858909527375,\n", "  'difficulty': 0.09034858909527375,\n", "  'frequency': 0},\n", " '314.0': {'coefficient': -0.01101840801242009,\n", "  'difficulty': 0.01101840801242009,\n", "  'frequency': 0},\n", " '317.0': {'coefficient': 0.030324769750500555,\n", "  'difficulty': -0.030324769750500555,\n", "  'frequency': 0},\n", " '321.0': {'coefficient': -0.02106316632441417,\n", "  'difficulty': 0.02106316632441417,\n", "  'frequency': 0},\n", " '322.0': {'coefficient': 0.03793416283210171,\n", "  'difficulty': -0.03793416283210171,\n", "  'frequency': 0},\n", " '323.0': {'coefficient': 0.02971580280589126,\n", "  'difficulty': -0.02971580280589126,\n", "  'frequency': 0},\n", " '324.0': {'coefficient': -0.0016198381246118466,\n", "  'difficulty': 0.0016198381246118466,\n", "  'frequency': 0},\n", " '325.0': {'coefficient': -0.08999619378908587,\n", "  'difficulty': 0.08999619378908587,\n", "  'frequency': 0},\n", " '331.0': {'coefficient': 0.01899989177655631,\n", "  'difficulty': -0.01899989177655631,\n", "  'frequency': 0},\n", " '333.0': {'coefficient': 0.00021337178893276242,\n", "  'difficulty': -0.00021337178893276242,\n", "  'frequency': 0},\n", " '334.0': {'coefficient': -0.00020674589929668433,\n", "  'difficulty': 0.00020674589929668433,\n", "  'frequency': 0},\n", " '340.0': {'coefficient': 0.005512155938894222,\n", "  'difficulty': -0.005512155938894222,\n", "  'frequency': 0},\n", " '343.0': {'coefficient': 0.007972608046860365,\n", "  'difficulty': -0.007972608046860365,\n", "  'frequency': 0},\n", " '346.0': {'coefficient': 0.002108318094966745,\n", "  'difficulty': -0.002108318094966745,\n", "  'frequency': 0},\n", " '348.0': {'coefficient': -0.0015571053730574027,\n", "  'difficulty': 0.0015571053730574027,\n", "  'frequency': 0},\n", " '350.0': {'coefficient': 0.013073016530757028,\n", "  'difficulty': -0.013073016530757028,\n", "  'frequency': 0},\n", " '356.0': {'coefficient': -0.0015220999933770818,\n", "  'difficulty': 0.0015220999933770818,\n", "  'frequency': 0},\n", " '362.0': {'coefficient': -0.010163686798467733,\n", "  'difficulty': 0.010163686798467733,\n", "  'frequency': 0},\n", " '365.0': {'coefficient': 0.0043361735034565954,\n", "  'difficulty': -0.0043361735034565954,\n", "  'frequency': 0},\n", " '368.0': {'coefficient': 0.07336515061030588,\n", "  'difficulty': -0.07336515061030588,\n", "  'frequency': 0},\n", " '371.0': {'coefficient': 0.0054860222522636485,\n", "  'difficulty': -0.0054860222522636485,\n", "  'frequency': 0},\n", " '375.0': {'coefficient': 0.021728049845083974,\n", "  'difficulty': -0.021728049845083974,\n", "  'frequency': 0},\n", " '378.0': {'coefficient': 0.03518385878110647,\n", "  'difficulty': -0.03518385878110647,\n", "  'frequency': 0}}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# pipeline.datasets['X_test']\n", "models['pfa'].skill_params"]}, {"cell_type": "code", "execution_count": 10, "id": "08b7e7d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.0\n", "\n", "1.0:\n", "  P(difficulty): -0.097\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.097\n", "2.0\n", "\n", "2.0:\n", "  P(difficulty): 0.033\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.033\n", "4.0\n", "\n", "4.0:\n", "  P(difficulty): -0.047\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.047\n", "5.0\n", "\n", "5.0:\n", "  P(difficulty): -0.020\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.020\n", "8.0\n", "\n", "8.0:\n", "  P(difficulty): -0.094\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.094\n", "9.0\n", "\n", "9.0:\n", "  P(difficulty): 0.014\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.014\n", "10.0\n", "\n", "10.0:\n", "  P(difficulty): 0.078\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.078\n", "11.0\n", "\n", "11.0:\n", "  P(difficulty): 0.067\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.067\n", "12.0\n", "\n", "12.0:\n", "  P(difficulty): 0.081\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.081\n", "13.0\n", "\n", "13.0:\n", "  P(difficulty): 0.006\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.006\n", "14.0\n", "\n", "14.0:\n", "  P(difficulty): -0.051\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.051\n", "15.0\n", "\n", "15.0:\n", "  P(difficulty): -0.046\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.046\n", "16.0\n", "\n", "16.0:\n", "  P(difficulty): 0.177\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.177\n", "17.0\n", "\n", "17.0:\n", "  P(difficulty): 0.172\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.172\n", "18.0\n", "\n", "18.0:\n", "  P(difficulty): -0.027\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.027\n", "21.0\n", "\n", "21.0:\n", "  P(difficulty): 0.042\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.042\n", "22.0\n", "\n", "22.0:\n", "  P(difficulty): 0.050\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.050\n", "24.0\n", "\n", "24.0:\n", "  P(difficulty): -0.074\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.074\n", "25.0\n", "\n", "25.0:\n", "  P(difficulty): -0.075\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.075\n", "26.0\n", "\n", "26.0:\n", "  P(difficulty): -0.019\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.019\n", "27.0\n", "\n", "27.0:\n", "  P(difficulty): 0.072\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.072\n", "32.0\n", "\n", "32.0:\n", "  P(difficulty): -0.059\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.059\n", "34.0\n", "\n", "34.0:\n", "  P(difficulty): 0.070\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.070\n", "35.0\n", "\n", "35.0:\n", "  P(difficulty): -0.057\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.057\n", "39.0\n", "\n", "39.0:\n", "  P(difficulty): -0.045\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.045\n", "40.0\n", "\n", "40.0:\n", "  P(difficulty): -0.066\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.066\n", "42.0\n", "\n", "42.0:\n", "  P(difficulty): -0.016\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.016\n", "43.0\n", "\n", "43.0:\n", "  P(difficulty): 0.066\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.066\n", "46.0\n", "\n", "46.0:\n", "  P(difficulty): -0.093\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.093\n", "47.0\n", "\n", "47.0:\n", "  P(difficulty): -0.205\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.205\n", "48.0\n", "\n", "48.0:\n", "  P(difficulty): 0.062\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.062\n", "49.0\n", "\n", "49.0:\n", "  P(difficulty): -0.008\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.008\n", "50.0\n", "\n", "50.0:\n", "  P(difficulty): 0.043\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.043\n", "51.0\n", "\n", "51.0:\n", "  P(difficulty): -0.066\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.066\n", "53.0\n", "\n", "53.0:\n", "  P(difficulty): -0.022\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.022\n", "54.0\n", "\n", "54.0:\n", "  P(difficulty): -0.040\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.040\n", "58.0\n", "\n", "58.0:\n", "  P(difficulty): 0.003\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.003\n", "61.0\n", "\n", "61.0:\n", "  P(difficulty): -0.111\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.111\n", "63.0\n", "\n", "63.0:\n", "  P(difficulty): 0.026\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.026\n", "64.0\n", "\n", "64.0:\n", "  P(difficulty): 0.001\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.001\n", "65.0\n", "\n", "65.0:\n", "  P(difficulty): -0.080\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.080\n", "67.0\n", "\n", "67.0:\n", "  P(difficulty): -0.109\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.109\n", "69.0\n", "\n", "69.0:\n", "  P(difficulty): -0.010\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.010\n", "70.0\n", "\n", "70.0:\n", "  P(difficulty): 0.003\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.003\n", "74.0\n", "\n", "74.0:\n", "  P(difficulty): 0.114\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.114\n", "75.0\n", "\n", "75.0:\n", "  P(difficulty): 0.021\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.021\n", "77.0\n", "\n", "77.0:\n", "  P(difficulty): 0.137\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.137\n", "79.0\n", "\n", "79.0:\n", "  P(difficulty): -0.005\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.005\n", "80.0\n", "\n", "80.0:\n", "  P(difficulty): -0.028\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.028\n", "81.0\n", "\n", "81.0:\n", "  P(difficulty): 0.159\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.159\n", "82.0\n", "\n", "82.0:\n", "  P(difficulty): -0.074\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.074\n", "83.0\n", "\n", "83.0:\n", "  P(difficulty): -0.056\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.056\n", "84.0\n", "\n", "84.0:\n", "  P(difficulty): -0.039\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.039\n", "85.0\n", "\n", "85.0:\n", "  P(difficulty): 0.066\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.066\n", "86.0\n", "\n", "86.0:\n", "  P(difficulty): -0.071\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.071\n", "92.0\n", "\n", "92.0:\n", "  P(difficulty): 0.066\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.066\n", "110.0\n", "\n", "110.0:\n", "  P(difficulty): -0.049\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.049\n", "163.0\n", "\n", "163.0:\n", "  P(difficulty): -0.016\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.016\n", "165.0\n", "\n", "165.0:\n", "  P(difficulty): -0.017\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.017\n", "166.0\n", "\n", "166.0:\n", "  P(difficulty): -0.025\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.025\n", "173.0\n", "\n", "173.0:\n", "  P(difficulty): -0.017\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.017\n", "190.0\n", "\n", "190.0:\n", "  P(difficulty): -0.015\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.015\n", "193.0\n", "\n", "193.0:\n", "  P(difficulty): -0.014\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.014\n", "203.0\n", "\n", "203.0:\n", "  P(difficulty): 0.034\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.034\n", "204.0\n", "\n", "204.0:\n", "  P(difficulty): 0.044\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.044\n", "217.0\n", "\n", "217.0:\n", "  P(difficulty): 0.041\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.041\n", "221.0\n", "\n", "221.0:\n", "  P(difficulty): -0.014\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.014\n", "276.0\n", "\n", "276.0:\n", "  P(difficulty): -0.072\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.072\n", "277.0\n", "\n", "277.0:\n", "  P(difficulty): -0.092\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.092\n", "278.0\n", "\n", "278.0:\n", "  P(difficulty): 0.162\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.162\n", "279.0\n", "\n", "279.0:\n", "  P(difficulty): 0.005\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.005\n", "280.0\n", "\n", "280.0:\n", "  P(difficulty): -0.167\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.167\n", "290.0\n", "\n", "290.0:\n", "  P(difficulty): -0.016\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.016\n", "292.0\n", "\n", "292.0:\n", "  P(difficulty): -0.004\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.004\n", "293.0\n", "\n", "293.0:\n", "  P(difficulty): -0.022\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.022\n", "294.0\n", "\n", "294.0:\n", "  P(difficulty): 0.040\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.040\n", "295.0\n", "\n", "295.0:\n", "  P(difficulty): -0.039\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.039\n", "296.0\n", "\n", "296.0:\n", "  P(difficulty): -0.035\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.035\n", "297.0\n", "\n", "297.0:\n", "  P(difficulty): 0.000\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.000\n", "298.0\n", "\n", "298.0:\n", "  P(difficulty): -0.032\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.032\n", "299.0\n", "\n", "299.0:\n", "  P(difficulty): -0.028\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.028\n", "301.0\n", "\n", "301.0:\n", "  P(difficulty): -0.061\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.061\n", "303.0\n", "\n", "303.0:\n", "  P(difficulty): -0.027\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.027\n", "307.0\n", "\n", "307.0:\n", "  P(difficulty): -0.078\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.078\n", "308.0\n", "\n", "308.0:\n", "  P(difficulty): 0.034\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.034\n", "309.0\n", "\n", "309.0:\n", "  P(difficulty): 0.021\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.021\n", "310.0\n", "\n", "310.0:\n", "  P(difficulty): 0.013\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.013\n", "311.0\n", "\n", "311.0:\n", "  P(difficulty): 0.078\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.078\n", "312.0\n", "\n", "312.0:\n", "  P(difficulty): 0.090\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.090\n", "314.0\n", "\n", "314.0:\n", "  P(difficulty): 0.011\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.011\n", "317.0\n", "\n", "317.0:\n", "  P(difficulty): -0.030\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.030\n", "321.0\n", "\n", "321.0:\n", "  P(difficulty): 0.021\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.021\n", "322.0\n", "\n", "322.0:\n", "  P(difficulty): -0.038\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.038\n", "323.0\n", "\n", "323.0:\n", "  P(difficulty): -0.030\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.030\n", "324.0\n", "\n", "324.0:\n", "  P(difficulty): 0.002\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.002\n", "325.0\n", "\n", "325.0:\n", "  P(difficulty): 0.090\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.090\n", "331.0\n", "\n", "331.0:\n", "  P(difficulty): -0.019\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.019\n", "333.0\n", "\n", "333.0:\n", "  P(difficulty): -0.000\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.000\n", "334.0\n", "\n", "334.0:\n", "  P(difficulty): 0.000\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.000\n", "340.0\n", "\n", "340.0:\n", "  P(difficulty): -0.006\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.006\n", "343.0\n", "\n", "343.0:\n", "  P(difficulty): -0.008\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.008\n", "346.0\n", "\n", "346.0:\n", "  P(difficulty): -0.002\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.002\n", "348.0\n", "\n", "348.0:\n", "  P(difficulty): 0.002\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.002\n", "350.0\n", "\n", "350.0:\n", "  P(difficulty): -0.013\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.013\n", "356.0\n", "\n", "356.0:\n", "  P(difficulty): 0.002\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.002\n", "362.0\n", "\n", "362.0:\n", "  P(difficulty): 0.010\n", "  P(frequency): 0.000\n", "  P(coefficient): -0.010\n", "365.0\n", "\n", "365.0:\n", "  P(difficulty): -0.004\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.004\n", "368.0\n", "\n", "368.0:\n", "  P(difficulty): -0.073\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.073\n", "371.0\n", "\n", "371.0:\n", "  P(difficulty): -0.005\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.005\n", "375.0\n", "\n", "375.0:\n", "  P(difficulty): -0.022\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.022\n", "378.0\n", "\n", "378.0:\n", "  P(difficulty): -0.035\n", "  P(frequency): 0.000\n", "  P(coefficient): 0.035\n"]}], "source": ["all_skills = sorted(df_clean['skill_id'].unique())\n", "skill_col_names = [f'skill_{skill}' for skill in all_skills]\n", "\n", "# skill_col_names\n", "\n", "# for skill in models['pfa'].skill_params:\n", "#     print(skill)\n", "#     #print skill variable type\n", "#     print(type(skill))\n", "\n", "for skill in all_skills:    \n", "\n", "    skill = str(skill)\n", "    # # Check if skill is in model convert types to match\n", "    # if skill in models['pfa'].skill_params:\n", "    #     print(f\"{skill} is in model\")\n", "    # else:\n", "    #     print(f\"{skill} is NOT in model\")\n", "        \n", "    if skill in models['pfa'].skill_params:\n", "        print(skill)\n", "        params = models['pfa'].skill_params[skill]\n", "\n", "        print(f\"\\n{skill}:\")\n", "        print(f\"  P(difficulty): {params['difficulty']:.3f}\")\n", "        print(f\"  P(frequency): {params['frequency']:.3f}\")\n", "        print(f\"  P(coefficient): {params['coefficient']:.3f}\")"]}, {"cell_type": "code", "execution_count": 11, "id": "02afc2a1", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-17 17:20:14,364 - INFO - Saving models to /home/<USER>/workspace/AClass/App/Training/models/outputs...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "💾 Saving models...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-17 17:20:18,594 - INFO - Saved pfa model to /home/<USER>/workspace/AClass/App/Training/models/outputs/pfa_model.joblib\n", "2025-07-17 17:20:18,596 - INFO - Saved metrics to /home/<USER>/workspace/AClass/App/Training/models/outputs/model_metrics.json\n"]}, {"name": "stdout", "output_type": "stream", "text": ["PFA model saved to /home/<USER>/workspace/AClass/App/Training/models/outputs/pfa_model.joblib\n", "✅ Models saved successfully\n"]}], "source": ["# Save models\n", "print(\"\\n💾 Saving models...\")\n", "# os.makedirs('App/Training/pipeline/outputs/math', exist_ok=True)\n", "OUTPUT_DIR='/home/<USER>/workspace/AClass/App/Training/models/outputs'\n", "pipeline.save_models(OUTPUT_DIR)\n", "print(\"✅ Models saved successfully\")"]}, {"cell_type": "code", "execution_count": 12, "id": "5194ba79", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "💾 Saving Skills parameters to /home/<USER>/workspace/AClass/App/Training/models/outputs/pfa_skills_parameters.csv\n"]}], "source": ["all_skills = sorted(df_clean['skill_id'].unique())\n", "# skill_col_names = [f'skill_{skill}' for skill in all_skills]\n", "\n", "top_skills_list_df=[]\n", "top_skills_list = skills_stats.index.tolist()\n", "for skill in all_skills:\n", "    skill = str(skill)\n", "    if skill in models['pfa'].skill_params:\n", "        params = models['pfa'].skill_params[skill]\n", "        top_skills_list_df.append([skill, params['difficulty'], params['frequency'], params['coefficient']])\n", "\n", "print(f\"\\n💾 Saving Skills parameters to {OUTPUT_DIR}/pfa_skills_parameters.csv\")\n", "#convert top_skills_list_df to dataframe with column names \n", "# column_names=['skill_name', 'p_init', 'p_learn', 'p_guess', 'p_slip']\n", "# top_skills_list_df = pd.DataFrame(top_skills_list_df, columns=column_names)\n", "\n", "top_skills_list_df = pd.DataFrame(top_skills_list_df,columns=['skill_name', 'difficulty', 'frequency', 'coefficient'])\n", "\n", "#save to TSV file\n", "top_skills_list_df.to_csv(OUTPUT_DIR+'/pfa_skills_parameters.csv', sep='\\t', index=False)"]}, {"cell_type": "code", "execution_count": 13, "id": "aec60eb0", "metadata": {}, "outputs": [], "source": ["def analyze_model_parameters(pfa_model):\n", "    \"\"\"\n", "    Analyze trained model parameters\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*50)\n", "    print(\"MODEL PARAMETER ANALYSIS\")\n", "    print(\"=\"*50)\n", "    \n", "    # Feature importance\n", "    print(\"\\nFeature Importance (Coefficients):\")\n", "    feature_names = ['success_count', 'failure_count', 'total_attempts', \n", "                    'attempt_count', 'hint_count', 'ms_first_response', 'opportunity']\n", "    \n", "    for i, feature in enumerate(feature_names):\n", "        if i < len(pfa_model.model.coef_[0]):\n", "            coef = pfa_model.model.coef_[0][i]\n", "            print(f\"{feature}: {coef:.4f}\")\n", "    \n", "    # Skill difficulty analysis\n", "    print(f\"\\nSkill Difficulty Analysis:\")\n", "    print(f\"Total skills analyzed: {len(pfa_model.skill_params)}\")\n", "    \n", "    if pfa_model.skill_params:\n", "        sorted_skills = sorted(pfa_model.skill_params.items(), \n", "                              key=lambda x: x[1]['difficulty'], reverse=True)\n", "        \n", "        print(\"\\nTop 5 Most Difficult Skills:\")\n", "        for skill_id, params in sorted_skills[:5]:\n", "            print(f\"Skill {skill_id}: Difficulty = {params['difficulty']:.4f}, \"\n", "                  f\"Frequency = {params['frequency']}\")\n", "        \n", "        print(\"\\nTop 5 Easiest Skills:\")\n", "        for skill_id, params in sorted_skills[-5:]:\n", "            print(f\"Skill {skill_id}: Difficulty = {params['difficulty']:.4f}, \"\n", "                  f\"Frequency = {params['frequency']}\")\n", "\n", "\n", "def analyze_user_mastery(pfa_model, top_n=5):\n", "    \"\"\"\n", "    Analyze user mastery levels\n", "    \"\"\"\n", "    print(f\"\\n\" + \"=\"*50)\n", "    print(\"USER MASTERY ANALYSIS\")\n", "    print(\"=\"*50)\n", "    \n", "    # Calculate overall user performance\n", "    user_performance = {}\n", "    for user_id, skills in pfa_model.user_skill_history.items():\n", "        total_attempts = sum(skill['total_attempts'] for skill in skills.values())\n", "        total_correct = sum(skill['correct_attempts'] for skill in skills.values())\n", "        avg_mastery = np.mean([skill['mastery_level'] for skill in skills.values()])\n", "        \n", "        user_performance[user_id] = {\n", "            'total_attempts': total_attempts,\n", "            'accuracy': total_correct / total_attempts if total_attempts > 0 else 0,\n", "            'skills_practiced': len(skills),\n", "            'avg_mastery': avg_mastery\n", "        }\n", "    \n", "    # Sort by average mastery\n", "    sorted_users = sorted(user_performance.items(), \n", "                         key=lambda x: x[1]['avg_mastery'], reverse=True)\n", "    \n", "    print(f\"\\nTop {top_n} Users by Mastery Level:\")\n", "    for user_id, performance in sorted_users[:top_n]:\n", "        print(f\"User {user_id}: Mastery = {performance['avg_mastery']:.3f}, \"\n", "              f\"Accuracy = {performance['accuracy']:.3f}, \"\n", "              f\"Skills = {performance['skills_practiced']}\")\n", "\n", "\n", "def generate_training_stats(df, pfa_model):\n", "    \"\"\"\n", "    Generate comprehensive training statistics\n", "    \"\"\"\n", "    print(f\"\\n\" + \"=\"*50)\n", "    print(\"TRAINING STATISTICS\")\n", "    print(\"=\"*50)\n", "    \n", "    # Overall statistics\n", "    print(f\"Total interactions: {len(df)}\")\n", "    print(f\"Total users: {df['user_id'].nunique()}\")\n", "    print(f\"Total skills: {df['skill_id'].nunique()}\")\n", "    print(f\"Total problems: {df['problem_id'].nunique()}\")\n", "    print(f\"Overall accuracy: {df['correct'].mean():.3f}\")\n", "    \n", "    # Skill statistics\n", "    print(f\"\\nSkill Statistics:\")\n", "    skill_stats = df.groupby('skill_name').agg({\n", "        'correct': ['count', 'mean', 'std'],\n", "        'attempt_count': 'mean',\n", "        'user_id': 'nunique'\n", "    }).round(3)\n", "    \n", "    skill_stats.columns = ['Total_Interactions', 'Accuracy', 'Accuracy_Std', \n", "                          'Avg_Attempts', 'Unique_Users']\n", "    \n", "    print(skill_stats.head(10))\n", "    \n", "    # Problem statistics\n", "    print(f\"\\nProblem Statistics (Top 10):\")\n", "    problem_stats = df.groupby('problem_id').agg({\n", "        'correct': ['count', 'mean'],\n", "        'attempt_count': 'mean',\n", "        'user_id': 'nunique'\n", "    }).round(3)\n", "    \n", "    problem_stats.columns = ['Total_Interactions', 'Accuracy', 'Avg_Attempts', 'Unique_Users']\n", "    problem_stats = problem_stats.sort_values('Total_Interactions', ascending=False)\n", "    \n", "    print(problem_stats.head(10))\n", "    \n", "    # Student statistics\n", "    print(f\"\\nStudent Statistics (Top 10 by activity):\")\n", "    student_stats = df.groupby('user_id').agg({\n", "        'correct': ['count', 'mean'],\n", "        'skill_id': 'nunique',\n", "        'problem_id': 'nunique'\n", "    }).round(3)\n", "    \n", "    student_stats.columns = ['Total_Interactions', 'Accuracy', 'Skills_Practiced', 'Problems_Solved']\n", "    student_stats = student_stats.sort_values('Total_Interactions', ascending=False)\n", "    \n", "    print(student_stats.head(10))\n", "\n", "\n", "def prediction_examples(pfa_model, X_test, df_test, n_examples=5):\n", "    \"\"\"\n", "    Show prediction examples for trained skills\n", "    \"\"\"\n", "    print(f\"\\n\" + \"=\"*50)\n", "    print(\"PREDICTION EXAMPLES\")\n", "    print(\"=\"*50)\n", "    \n", "    # Make predictions\n", "    predictions = pfa_model.predict(X_test)\n", "    \n", "    # Select random examples\n", "    indices = np.random.choice(len(X_test), n_examples, replace=False)\n", "    \n", "    print(f\"Showing {n_examples} prediction examples:\")\n", "    print()\n", "    \n", "    for i, idx in enumerate(indices):\n", "        actual = df_test.iloc[idx]['correct']\n", "        predicted_prob = predictions[idx]\n", "        predicted_class = 1 if predicted_prob > 0.5 else 0\n", "        \n", "        user_id = df_test.iloc[idx]['user_id']\n", "        skill_id = df_test.iloc[idx]['skill_id']\n", "        success_count = X_test.iloc[idx]['success_count']\n", "        failure_count = X_test.iloc[idx]['failure_count']\n", "        \n", "        print(f\"Example {i+1}:\")\n", "        print(f\"  User: {user_id}, Skill: {skill_id}\")\n", "        print(f\"  Prior Success: {success_count}, Prior Failures: {failure_count}\")\n", "        print(f\"  Predicted Probability: {predicted_prob:.3f}\")\n", "        print(f\"  Predicted Class: {predicted_class}\")\n", "        print(f\"  Actual: {actual}\")\n", "        print(f\"  Correct: {'✓' if predicted_class == actual else '✗'}\")\n", "        print()\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "id": "f213b24b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "MODEL PARAMETER ANALYSIS\n", "==================================================\n", "\n", "Feature Importance (Coefficients):\n", "success_count: 0.4848\n", "failure_count: -0.5316\n", "total_attempts: 0.0055\n", "attempt_count: -27.9998\n", "hint_count: -7.5071\n", "ms_first_response: -0.0527\n", "opportunity: 0.0359\n", "\n", "Skill Difficulty Analysis:\n", "Total skills analyzed: 111\n", "\n", "Top 5 Most Difficult Skills:\n", "Skill 16.0: Difficulty = 0.1768, Frequency = 0\n", "Skill 17.0: Difficulty = 0.1719, Frequency = 0\n", "Skill 278.0: Difficulty = 0.1621, Frequency = 0\n", "Skill 81.0: Difficulty = 0.1591, Frequency = 0\n", "Skill 77.0: Difficulty = 0.1366, Frequency = 0\n", "\n", "Top 5 Easiest Skills:\n", "Skill 1.0: Difficulty = -0.0968, Frequency = 0\n", "Skill 67.0: Difficulty = -0.1094, Frequency = 0\n", "Skill 61.0: Difficulty = -0.1110, Frequency = 0\n", "Skill 280.0: Difficulty = -0.1668, Frequency = 0\n", "Skill 47.0: Difficulty = -0.2055, Frequency = 0\n"]}], "source": ["# 8. Analyze model parameters\n", "analyze_model_parameters(models['pfa'])"]}, {"cell_type": "code", "execution_count": 15, "id": "5cf846d7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "USER MASTERY ANALYSIS\n", "==================================================\n", "\n", "Top 5 Users by Mastery Level:\n", "User 82672: Mastery = 1.000, Accuracy = 0.576, Skills = 1\n", "User 82032: Mastery = 1.000, Accuracy = 0.714, Skills = 1\n", "User 86678: Mastery = 1.000, Accuracy = 1.000, Skills = 1\n", "User 83277: Mastery = 1.000, Accuracy = 0.526, Skills = 1\n", "User 81624: Mastery = 1.000, Accuracy = 0.915, Skills = 1\n"]}], "source": ["# 9. Analyze user mastery\n", "analyze_user_mastery(models['pfa'])"]}, {"cell_type": "code", "execution_count": 16, "id": "b35ea8e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "TRAINING STATISTICS\n", "==================================================\n", "Total interactions: 325637\n", "Total users: 4151\n", "Total skills: 111\n", "Total problems: 16891\n", "Overall accuracy: 0.658\n", "\n", "Skill Statistics:\n", "                                               Total_Interactions  Accuracy  \\\n", "skill_name                                                                    \n", "Absolute Value                                               7340     0.757   \n", "Addition Whole Numbers                                       4704     0.761   \n", "Addition and Subtraction Fractions                          11334     0.677   \n", "Addition and Subtraction Integers                           12741     0.599   \n", "Addition and Subtraction Positive Decimals                   5012     0.580   \n", "Algebraic Simplification                                       90     0.400   \n", "Algebraic Solving                                             389     0.368   \n", "Angles - Obtuse, <PERSON>cute, and Right                             305     0.600   \n", "Angles on Parallel Lines Cut by a Transversal                 278     0.802   \n", "Area Circle                                                  1149     0.683   \n", "\n", "                                               Accuracy_Std  Avg_Attempts  \\\n", "skill_name                                                                  \n", "Absolute Value                                        0.429         1.048   \n", "Addition Whole Numbers                                0.427         1.202   \n", "Addition and Subtraction Fractions                    0.468         1.843   \n", "Addition and Subtraction Integers                     0.490         2.123   \n", "Addition and Subtraction Positive Decimals            0.494         0.913   \n", "Algebraic Simplification                              0.493         1.656   \n", "Algebraic Solving                                     0.483         3.031   \n", "Angles - Obtuse, <PERSON><PERSON>, and Right                     0.491         1.397   \n", "Angles on Parallel Lines Cut by a Transversal         0.399         1.076   \n", "Area Circle                                           0.465         1.688   \n", "\n", "                                               Unique_Users  \n", "skill_name                                                   \n", "Absolute Value                                         1002  \n", "Addition Whole Numbers                                  988  \n", "Addition and Subtraction Fractions                     1353  \n", "Addition and Subtraction Integers                      1226  \n", "Addition and Subtraction Positive Decimals              753  \n", "Algebraic Simplification                                 15  \n", "Algebraic Solving                                        88  \n", "Angles - Obtuse, <PERSON><PERSON>, and Right                        11  \n", "Angles on Parallel Lines Cut by a Transversal           140  \n", "Area Circle                                             264  \n", "\n", "Problem Statistics (Top 10):\n", "            Total_Interactions  Accuracy  Avg_Attempts  Unique_Users\n", "problem_id                                                          \n", "49325                      272     0.765         0.941           116\n", "49313                      272     0.735         0.868           114\n", "49373                      270     0.726         0.896           106\n", "49319                      270     0.726         0.889           114\n", "49331                      262     0.718         0.939           106\n", "49352                      260     0.769         0.862           103\n", "49328                      250     0.720         0.936           106\n", "49301                      248     0.677         0.919           100\n", "49292                      248     0.815         0.911           102\n", "49376                      248     0.710         0.903            91\n", "\n", "Student Statistics (Top 10 by activity):\n", "         Total_Interactions  Accuracy  Skills_Practiced  Problems_Solved\n", "user_id                                                                 \n", "79021                  1261     0.552                71             1000\n", "75169                  1235     0.806                83              942\n", "78970                  1177     0.589                68              961\n", "79032                  1146     0.476                67              923\n", "71881                  1118     0.714                80              876\n", "96274                  1103     0.482                66              901\n", "78979                  1093     0.549                66              915\n", "79029                  1083     0.519                69              777\n", "78978                  1066     0.591                69              842\n", "96244                  1066     0.554                65              901\n"]}], "source": ["# 10. Generate training statistics\n", "generate_training_stats(df_clean, models['pfa'])\n"]}, {"cell_type": "code", "execution_count": 18, "id": "e9b1c7f8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "PREDICTION EXAMPLES\n", "==================================================\n", "Showing 5 prediction examples:\n", "\n", "Example 1:\n", "  User: 78572, Skill: 74.0\n", "  Prior Success: 0, Prior Failures: 0\n", "  Predicted Probability: 0.702\n", "  Predicted Class: 1\n", "  Actual: 0\n", "  Correct: ✗\n", "\n", "Example 2:\n", "  User: 96274, Skill: 4.0\n", "  Prior Success: 2, Prior Failures: 0\n", "  Predicted Probability: 0.000\n", "  Predicted Class: 0\n", "  Actual: 0\n", "  Correct: ✓\n", "\n", "Example 3:\n", "  User: 89252, Skill: 86.0\n", "  Prior Success: 1, Prior Failures: 2\n", "  Predicted Probability: 0.000\n", "  Predicted Class: 0\n", "  Actual: 0\n", "  Correct: ✓\n", "\n", "Example 4:\n", "  User: 79483, Skill: 65.0\n", "  Prior Success: 5, Prior Failures: 2\n", "  Predicted Probability: 0.927\n", "  Predicted Class: 1\n", "  Actual: 1\n", "  Correct: ✓\n", "\n", "Example 5:\n", "  User: 73685, Skill: 279.0\n", "  Prior Success: 12, Prior Failures: 0\n", "  Predicted Probability: 0.900\n", "  Predicted Class: 1\n", "  Actual: 1\n", "  Correct: ✓\n", "\n"]}], "source": ["# 11. Show prediction examples\n", "\n", "\n", "prediction_examples(models['pfa'], pipeline.datasets['X_test'], pipeline.datasets['df_test'])"]}, {"cell_type": "code", "execution_count": null, "id": "5e8cd2ee", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}