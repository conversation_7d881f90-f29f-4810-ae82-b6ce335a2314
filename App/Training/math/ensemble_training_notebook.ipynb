{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ensemble Knowledge Tracing Training Notebook\n", "\n", "This notebook demonstrates the training and evaluation of ensemble models for knowledge tracing using stacking and voting approaches. It combines BKT, PFA, and DKT models to create more robust prediction systems.\n", "\n", "## Overview\n", "- Load pre-trained individual models (BKT, PFA, DKT)\n", "- Create ensemble models using different strategies\n", "- Evaluate and compare performance\n", "- Generate comprehensive metrics comparison\n", "\n", "## Requirements\n", "- Pre-trained models in `../models/outputs/`\n", "- Math dataset: `skill_builder_data_corrected.csv`"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 18:07:29.205246: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📚 Libraries imported successfully!\n", "Current working directory: /home/<USER>/workspace/AClass/App/Training/math\n"]}], "source": ["# Import required libraries\n", "import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import joblib\n", "import json\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add paths for imports\n", "sys.path.append('../pipeline')\n", "sys.path.append('../models')\n", "\n", "# Import custom modules\n", "from train_ensemble import EnsembleTrainer\n", "from kt_ensemble import KTModelEnsemble\n", "from kt_evaluation import KTEvaluator\n", "\n", "# Set up plotting\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"📚 Libraries imported successfully!\")\n", "print(f\"Current working directory: {os.getcwd()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Initialize Ensemble Trainer and <PERSON>ad Data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 18:07:31,284 - INFO - Loading dataset...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🚀 Initializing Ensemble Trainer...\n", "\n", "📊 Loading dataset...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-19 18:07:32,593 - INFO - Dataset loaded: (401756, 30)\n", "2025-07-19 18:07:32,594 - INFO - Cleaning data...\n", "2025-07-19 18:07:32,787 - INFO - Data cleaned: (325637, 30)\n", "2025-07-19 18:07:32,807 - INFO - Train set: (260509, 30)\n", "2025-07-19 18:07:32,808 - INFO - Test set: (65128, 30)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "✅ Data loaded successfully!\n", "Full dataset: (325637, 30)\n", "Training set: (260509, 30)\n", "Test set: (65128, 30)\n", "\n", "📈 Dataset Statistics:\n", "Total students: 4,151\n", "Total skills: 110\n", "Total interactions: 325,637\n", "Overall accuracy: 0.658\n"]}], "source": ["# Initialize ensemble trainer\n", "print(\"🚀 Initializing Ensemble Trainer...\")\n", "trainer = <PERSON><PERSON>rainer(\n", "    models_dir=\"../models/outputs\",\n", "    data_path=\"../datasets/math/skill_builder_data_corrected.csv\"\n", ")\n", "\n", "# Load and explore data\n", "print(\"\\n📊 Loading dataset...\")\n", "df_full, train_df, test_df = trainer.load_data()\n", "\n", "print(f\"\\n✅ Data loaded successfully!\")\n", "print(f\"Full dataset: {df_full.shape}\")\n", "print(f\"Training set: {train_df.shape}\")\n", "print(f\"Test set: {test_df.shape}\")\n", "\n", "# Display basic statistics\n", "print(\"\\n📈 Dataset Statistics:\")\n", "print(f\"Total students: {df_full['user_id'].nunique():,}\")\n", "print(f\"Total skills: {df_full['skill_name'].nunique():,}\")\n", "print(f\"Total interactions: {len(df_full):,}\")\n", "print(f\"Overall accuracy: {df_full['correct'].mean():.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Load Pre-trained Models"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 18:07:32,850 - INFO - Loading pre-trained models...\n", "2025-07-19 18:07:32,859 - INFO - ✅ BKT model loaded\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🔄 Loading pre-trained models...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-19 18:07:34,311 - INFO - ✅ PFA model loaded\n", "2025-07-19 18:07:34,313 - INFO - ✅ DKT model loaded\n", "2025-07-19 18:07:34,314 - INFO - Loaded 3 models: ['bkt', 'pfa', 'dkt']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "✅ Loaded 3 models:\n", "  - BKT\n", "  - PFA\n", "  - DKT\n", "\n", "🎯 Ready for ensemble training with 3 models!\n"]}], "source": ["# Load pre-trained models\n", "print(\"🔄 Loading pre-trained models...\")\n", "models = trainer.load_trained_models()\n", "\n", "print(f\"\\n✅ Loaded {len(models)} models:\")\n", "for model_name in models.keys():\n", "    print(f\"  - {model_name.upper()}\")\n", "\n", "# Check model availability\n", "if len(models) < 2:\n", "    print(\"\\n⚠️ Warning: Need at least 2 models for ensemble training!\")\n", "    print(\"Please ensure BKT, PFA, and DKT models are trained and saved.\")\n", "else:\n", "    print(f\"\\n🎯 Ready for ensemble training with {len(models)} models!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON><PERSON>ate Individual Models"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 18:07:38,739 - INFO - Evaluating individual models...\n", "2025-07-19 18:07:38,750 - INFO - Using 65128 samples for evaluation\n", "2025-07-19 18:07:38,752 - INFO - Evaluating bkt model...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📊 Evaluating individual models...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-19 18:07:42,676 - INFO - bkt metrics: {'accuracy': 0.6065, 'precision': 0.7253, 'recall': 0.6993, 'f1_score': 0.7121, 'auc_roc': 0.5663}\n", "2025-07-19 18:07:42,677 - INFO - Evaluating pfa model...\n", "2025-07-19 18:07:46,025 - INFO - pfa metrics: {'accuracy': 0.3041, 'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0, 'auc_roc': 0.5}\n", "2025-07-19 18:07:46,026 - INFO - Evaluating dkt model...\n", "2025-07-19 18:07:49,447 - INFO - dkt metrics: {'accuracy': 0.3041, 'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0, 'auc_roc': 0.5}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📈 Individual Model Performance:\n", "==================================================\n", "\n", "BKT:\n", "  accuracy: 0.6065\n", "  precision: 0.7253\n", "  recall: 0.6993\n", "  f1_score: 0.7121\n", "  auc_roc: 0.5663\n", "\n", "PFA:\n", "  accuracy: 0.3041\n", "  precision: 0.0000\n", "  recall: 0.0000\n", "  f1_score: 0.0000\n", "  auc_roc: 0.5000\n", "\n", "DKT:\n", "  accuracy: 0.3041\n", "  precision: 0.0000\n", "  recall: 0.0000\n", "  f1_score: 0.0000\n", "  auc_roc: 0.5000\n"]}], "source": ["# Evaluate individual models on test set\n", "print(\"📊 Evaluating individual models...\")\n", "individual_metrics = trainer.evaluate_individual_models(test_df)\n", "\n", "# Display results\n", "print(\"\\n📈 Individual Model Performance:\")\n", "print(\"=\" * 50)\n", "\n", "for model_name, metrics in individual_metrics.items():\n", "    if 'error' not in metrics:\n", "        print(f\"\\n{model_name.upper()}:\")\n", "        for metric, value in metrics.items():\n", "            print(f\"  {metric}: {value:.4f}\")\n", "    else:\n", "        print(f\"\\n{model_name.upper()}: Error - {metrics['error']}\")\n", "\n", "# Store metrics for later comparison\n", "trainer.metrics.update(individual_metrics)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Train Ensemble Models"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 18:07:59,430 - INFO - Training ensemble models...\n", "2025-07-19 18:07:59,461 - INFO - Training weighted average ensemble...\n", "2025-07-19 18:07:59,462 - INFO - Training ensemble model using weighted_average...\n", "2025-07-19 18:07:59,463 - INFO - Training weighted average ensemble...\n", "2025-07-19 18:07:59,465 - INFO - Getting predictions from bkt model...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🔧 Training ensemble models...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-19 18:14:50,458 - INFO - Getting predictions from pfa model...\n", "2025-07-19 18:14:50,461 - INFO - Getting predictions from dkt model...\n", "2025-07-19 18:20:07,220 - INFO - Getting predictions from bkt model...\n", "2025-07-19 18:21:22,662 - INFO - Getting predictions from pfa model...\n", "2025-07-19 18:21:22,664 - INFO - Getting predictions from dkt model...\n", "2025-07-19 18:22:15,224 - INFO - Optimal weights: {'bkt': 0.8, 'pfa': 0.1, 'dkt': 0.09999999999999995}\n", "2025-07-19 18:22:15,225 - INFO - Ensemble training completed\n", "2025-07-19 18:22:15,226 - INFO - Weighted average metrics: {'accuracy': 0.6808592310526962, 'precision': 0.7384214720230113, 'recall': 0.8383495145631068, 'f1_score': 0.7852190177012183, 'auc': 0.6106372967781077}\n", "2025-07-19 18:22:15,226 - INFO - Training stacking ensemble...\n", "2025-07-19 18:22:15,227 - INFO - Training ensemble model using stacking...\n", "2025-07-19 18:22:15,228 - INFO - Training stacking ensemble...\n", "2025-07-19 18:22:15,229 - INFO - Getting predictions from bkt model...\n", "2025-07-19 18:29:33,350 - INFO - Getting predictions from pfa model...\n", "2025-07-19 18:29:33,353 - INFO - Getting predictions from dkt model...\n", "2025-07-19 18:34:38,989 - INFO - Getting predictions from bkt model...\n", "2025-07-19 18:35:48,192 - INFO - Getting predictions from pfa model...\n", "2025-07-19 18:35:48,193 - INFO - Getting predictions from dkt model...\n", "2025-07-19 18:36:34,059 - INFO - Ensemble training completed\n", "2025-07-19 18:36:34,060 - INFO - Stacking metrics: {'accuracy': 0.7126735044834787, 'precision': 0.735156346667138, 'recall': 0.9176963812886143, 'f1_score': 0.8163465596262747, 'auc': 0.6145751364466931}\n", "2025-07-19 18:36:34,060 - INFO - Training voting ensemble...\n", "2025-07-19 18:36:34,061 - INFO - Training ensemble model using voting...\n", "2025-07-19 18:36:34,062 - INFO - Training voting ensemble...\n", "2025-07-19 18:36:34,063 - INFO - Getting predictions from bkt model...\n", "2025-07-19 18:37:42,569 - INFO - Getting predictions from pfa model...\n", "2025-07-19 18:37:42,571 - INFO - Getting predictions from dkt model...\n", "2025-07-19 18:38:27,513 - INFO - Ensemble training completed\n", "2025-07-19 18:38:27,514 - INFO - Voting metrics: {'accuracy': 0.48186647831961676, 'precision': 0.7396976599710084, 'recall': 0.3940864960282436, 'f1_score': 0.5142157921255308, 'auc': 0.5383952270125063}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🎯 Ensemble Model Performance:\n", "==================================================\n", "\n", "WEIGHTED_AVERAGE ENSEMBLE:\n", "  accuracy: 0.6809\n", "  precision: 0.7384\n", "  recall: 0.8383\n", "  f1_score: 0.7852\n", "  auc: 0.6106\n", "\n", "STACKING ENSEMBLE:\n", "  accuracy: 0.7127\n", "  precision: 0.7352\n", "  recall: 0.9177\n", "  f1_score: 0.8163\n", "  auc: 0.6146\n", "\n", "VOTING ENSEMBLE:\n", "  accuracy: 0.4819\n", "  precision: 0.7397\n", "  recall: 0.3941\n", "  f1_score: 0.5142\n", "  auc: 0.5384\n"]}], "source": ["# Train different ensemble models\n", "print(\"🔧 Training ensemble models...\")\n", "ensemble_metrics = trainer.train_ensemble_models(train_df, test_df)\n", "\n", "# Display ensemble results\n", "print(\"\\n🎯 Ensemble Model Performance:\")\n", "print(\"=\" * 50)\n", "\n", "for ensemble_name, metrics in ensemble_metrics.items():\n", "    print(f\"\\n{ensemble_name.upper()} ENSEMBLE:\")\n", "    for metric, value in metrics.items():\n", "        print(f\"  {metric}: {value:.4f}\")\n", "\n", "# Add ensemble metrics to trainer\n", "for ensemble_name, metrics in ensemble_metrics.items():\n", "    trainer.metrics[f'ensemble_{ensemble_name}'] = metrics"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Visualize Performance Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create performance comparison visualizations\n", "print(\"📊 Creating performance visualizations...\")\n", "\n", "# Prepare data for plotting\n", "plot_data = []\n", "for model_name, metrics in trainer.metrics.items():\n", "    if 'error' not in metrics:\n", "        model_type = 'Ensemble' if model_name.startswith('ensemble_') else 'Individual'\n", "        display_name = model_name.replace('ensemble_', '').upper()\n", "        \n", "        for metric, value in metrics.items():\n", "            plot_data.append({\n", "                'Model': display_name,\n", "                'Type': model_type,\n", "                'Metric': metric.replace('_', ' ').title(),\n", "                'Value': value\n", "            })\n", "\n", "plot_df = pd.DataFrame(plot_data)\n", "\n", "# Create subplots for different metrics\n", "metrics_to_plot = ['Accuracy', 'F1 Score', 'Auc Roc', 'Precision', 'Recall']\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "axes = axes.flatten()\n", "\n", "for i, metric in enumerate(metrics_to_plot):\n", "    if i < len(axes):\n", "        metric_data = plot_df[plot_df['Metric'] == metric]\n", "        \n", "        if not metric_data.empty:\n", "            sns.barplot(data=metric_data, x='Model', y='Value', hue='Type', ax=axes[i])\n", "            axes[i].set_title(f'{metric} Comparison', fontsize=14, fontweight='bold')\n", "            axes[i].set_xlabel('Model', fontsize=12)\n", "            axes[i].set_ylabel(metric, fontsize=12)\n", "            axes[i].tick_params(axis='x', rotation=45)\n", "            axes[i].legend(title='Model Type')\n", "            \n", "            # Add value labels on bars\n", "            for container in axes[i].containers:\n", "                axes[i].bar_label(container, fmt='%.3f', fontsize=10)\n", "\n", "# Remove empty subplot\n", "if len(metrics_to_plot) < len(axes):\n", "    fig.delaxes(axes[-1])\n", "\n", "plt.tight_layout()\n", "plt.suptitle('Knowledge Tracing Models Performance Comparison', fontsize=16, fontweight='bold', y=1.02)\n", "plt.show()\n", "\n", "print(\"✅ Performance visualizations created!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Save Models and Generate Report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save ensemble models\n", "print(\"💾 Saving ensemble models...\")\n", "trainer.save_ensemble_models()\n", "\n", "# Generate comprehensive report\n", "print(\"\\n📋 Generating comparison report...\")\n", "report = trainer.generate_comparison_report()\n", "\n", "# Save report\n", "trainer.save_report(report)\n", "\n", "print(\"\\n✅ Models and reports saved successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Summary and Recommendations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display final summary\n", "print(\"🎯 ENSEMBLE TRAINING SUMMARY\")\n", "print(\"=\" * 50)\n", "\n", "if report:\n", "    print(f\"📊 Test Sample Size: {report['test_sample_size']:,}\")\n", "    print(f\"🔧 Models Trained: {report['summary']['total_models_trained']}\")\n", "    print(f\"🎯 Ensembles Trained: {report['summary']['total_ensembles_trained']}\")\n", "    print(f\"\\n🏆 Best Individual Model: {report['best_model']} (F1: {report['summary']['best_individual_f1']:.4f})\")\n", "    print(f\"🏆 Best Ensemble Model: {report['best_ensemble']} (F1: {report['summary']['best_ensemble_f1']:.4f})\")\n", "    \n", "    # Performance improvement analysis\n", "    improvement = report['summary']['best_ensemble_f1'] - report['summary']['best_individual_f1']\n", "    improvement_pct = (improvement / report['summary']['best_individual_f1']) * 100\n", "    \n", "    print(f\"\\n📈 Performance Improvement:\")\n", "    print(f\"   F1 Score Improvement: {improvement:+.4f} ({improvement_pct:+.2f}%)\")\n", "    \n", "    if improvement > 0:\n", "        print(\"   ✅ Ensemble outperforms individual models!\")\n", "    else:\n", "        print(\"   ⚠️ Individual model performs better than ensemble.\")\n", "    \n", "    print(\"\\n💡 Recommendations:\")\n", "    if report['best_ensemble']:\n", "        print(f\"   - Use {report['best_ensemble']} ensemble for production\")\n", "        print(f\"   - Main ensemble model saved as 'ensemble_model.joblib'\")\n", "    print(f\"   - Consider {report['best_model']} as fallback individual model\")\n", "    print(\"   - Monitor performance on new data\")\n", "    print(\"   - Consider retraining with more recent data\")\n", "\n", "print(\"\\n📁 Output Files:\")\n", "print(\"   - ensemble_comparison_report.json\")\n", "print(\"   - ensemble_summary.txt\")\n", "print(\"   - ensemble_*_model.joblib (individual ensemble models)\")\n", "print(\"   - ensemble_model.joblib (best ensemble model)\")\n", "\n", "print(\"\\n🎉 Ensemble training completed successfully!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}