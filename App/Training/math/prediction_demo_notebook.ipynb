{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Knowledge Tracing Prediction Demo\n", "\n", "This notebook demonstrates how to use the knowledge tracing prediction system to predict student knowledge states and analyze learning mastery. It showcases real-time prediction capabilities for adaptive learning applications.\n", "\n", "## Features\n", "- Load and use trained KT models (BKT, PFA, DKT, Ensemble)\n", "- Predict student performance for specific skills\n", "- Analyze learning mastery states\n", "- Compare different model predictions\n", "- Simulate real-time learning scenarios\n", "\n", "## Use Cases\n", "- Real-time student assessment\n", "- Adaptive learning path recommendations\n", "- Learning analytics and progress tracking\n", "- Educational intervention triggers"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 17:33:10.940073: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📚 Libraries imported successfully!\n", "Current working directory: /home/<USER>/workspace/AClass/App/Training/math\n"]}], "source": ["# Import required libraries\n", "import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add pipeline directory to path\n", "sys.path.append('../pipeline')\n", "\n", "# Import prediction module\n", "from prediction import KTPredictor\n", "\n", "# Set up plotting\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"📚 Libraries imported successfully!\")\n", "print(f\"Current working directory: {os.getcwd()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Initialize Prediction System"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Initializing Knowledge Tracing Predictor...\n", "\n", "📊 Loading trained models...\n", "\n", "✅ Models loaded successfully!\n", "Available models: ['bkt', 'pfa', 'dkt', 'ensemble']\n", "\n", "🎯 Ready for prediction with 4 models!\n"]}], "source": ["# Initialize the knowledge tracing predictor\n", "print(\"🚀 Initializing Knowledge Tracing Predictor...\")\n", "predictor = KTPredictor(models_dir=\"../models/outputs\")\n", "\n", "# Load trained models\n", "print(\"\\n📊 Loading trained models...\")\n", "load_status = predictor.load_models()\n", "\n", "print(f\"\\n✅ Models loaded successfully!\")\n", "loaded_models = [model for model, status in load_status.items() if status]\n", "print(f\"Available models: {loaded_models}\")\n", "\n", "if not loaded_models:\n", "    print(\"⚠️ No models loaded. Please ensure models are trained and saved.\")\n", "else:\n", "    print(f\"\\n🎯 Ready for prediction with {len(loaded_models)} models!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Basic Prediction Examples"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📈 Example 1: Single Prediction\n", "========================================\n", "Student history: [1, 0, 1, 1, 0, 1]\n", "Target skill: Addition\n", "Recent performance: 0.67\n", "\n", "Predictions from different models:\n", "  BKT         : 0.270\n", "  PFA         : 0.600\n", "  DKT         : 0.671\n", "  ENSEMBLE    : 0.423\n"]}], "source": ["# Example 1: Single prediction\n", "print(\"📈 Example 1: Single Prediction\")\n", "print(\"=\" * 40)\n", "\n", "# Student interaction history (1=correct, 0=incorrect)\n", "user_history = [1, 0, 1, 1, 0, 1]\n", "target_skill = \"Addition\"\n", "\n", "print(f\"Student history: {user_history}\")\n", "print(f\"Target skill: {target_skill}\")\n", "print(f\"Recent performance: {np.mean(user_history[-3:]):.2f}\")\n", "\n", "# Get predictions from all available models\n", "print(\"\\nPredictions from different models:\")\n", "for model_name in loaded_models:\n", "    try:\n", "        prediction = predictor.predict_student_performance(\n", "            user_history=user_history,\n", "            skill=target_skill,\n", "            model_type=model_name\n", "        )\n", "        print(f\"  {model_name.upper():12}: {prediction:.3f}\")\n", "    except Exception as e:\n", "        print(f\"  {model_name.upper():12}: Error - {e}\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Example 2: Model Comparison Visualization\n", "=============================================\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📋 Summary Statistics:\n", "                              mean  std\n", "<PERSON><PERSON>                      \n", "BKT      Average Student     0.270  0.0\n", "         High Performer      0.270  0.0\n", "         Improving Student   0.270  0.0\n", "         Struggling Student  0.270  0.0\n", "DKT      Average Student     0.671  0.0\n", "         High Performer      0.788  0.0\n", "         Improving Student   0.646  0.0\n", "         Struggling Student  0.452  0.0\n", "ENSEMBLE Average Student     0.423  0.0\n", "         High Performer      0.478  0.0\n", "         Improving Student   0.416  0.0\n", "         Struggling Student  0.338  0.0\n", "PFA      Average Student     0.600  0.0\n", "         High Performer      0.800  0.0\n", "         Improving Student   0.600  0.0\n", "         Struggling Student  0.400  0.0\n"]}], "source": ["# Example 2: Model comparison visualization\n", "print(\"\\n📊 Example 2: Model Comparison Visualization\")\n", "print(\"=\" * 45)\n", "\n", "# Test different scenarios\n", "scenarios = {\n", "    \"Struggling Student\": [0, 0, 1, 0, 0, 1],\n", "    \"Average Student\": [1, 0, 1, 1, 0, 1],\n", "    \"High Performer\": [1, 1, 1, 1, 0, 1],\n", "    \"Improving Student\": [0, 0, 0, 1, 1, 1]\n", "}\n", "\n", "skills = [\"Addition\", \"Subtraction\", \"Multiplication\", \"Division\"]\n", "\n", "# Create comparison data\n", "comparison_data = []\n", "for scenario_name, history in scenarios.items():\n", "    for skill in skills:\n", "        for model_name in loaded_models:\n", "            try:\n", "                prediction = predictor.predict_student_performance(\n", "                    user_history=history,\n", "                    skill=skill,\n", "                    model_type=model_name\n", "                )\n", "                comparison_data.append({\n", "                    'Scenario': scenario_name,\n", "                    'Skill': skill,\n", "                    'Model': model_name.upper(),\n", "                    'Prediction': prediction,\n", "                    'Recent_Performance': np.mean(history[-3:])\n", "                })\n", "            except Exception as e:\n", "                print(f\"Error with {model_name} for {scenario_name}-{skill}: {e}\")\n", "\n", "# Convert to DataFrame\n", "comparison_df = pd.DataFrame(comparison_data)\n", "\n", "if not comparison_df.empty:\n", "    # Create visualization\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    axes = axes.flatten()\n", "    \n", "    for i, skill in enumerate(skills):\n", "        skill_data = comparison_df[comparison_df['Skill'] == skill]\n", "        if not skill_data.empty:\n", "            sns.barplot(data=skill_data, x='Scenario', y='Prediction', hue='Model', ax=axes[i])\n", "            axes[i].set_title(f'{skill} - Model Predictions', fontweight='bold')\n", "            axes[i].set_xlabel('Student Scenario')\n", "            axes[i].set_ylabel('Predicted Success Probability')\n", "            axes[i].tick_params(axis='x', rotation=45)\n", "            axes[i].legend(title='Model')\n", "            axes[i].set_ylim(0, 1)\n", "    \n", "    plt.tight_layout()\n", "    plt.suptitle('Knowledge Tracing Model Predictions Comparison', fontsize=16, fontweight='bold', y=1.02)\n", "    plt.show()\n", "    \n", "    print(\"\\n📋 Summary Statistics:\")\n", "    print(comparison_df.groupby(['Model', 'Sc<PERSON><PERSON>'])['Prediction'].agg(['mean', 'std']).round(3))\n", "else:\n", "    print(\"No comparison data available.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Learning Mastery State Analysis"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 Learning Mastery State Analysis\n", "========================================\n", "Using ENSEMBLE model for mastery analysis\n", "\n", "📊 Student A Mastery Analysis:\n", "------------------------------\n", "  Addition        🟠 Developing   (Score: 0.522)\n", "    💡 📖 Review Addition fundamentals and practice\n", "  Subtraction     🟠 Developing   (Score: 0.498)\n", "    💡 📖 Review Subtraction fundamentals and practice\n", "  Multiplication  🔴 Struggling   (Score: 0.318)\n", "    💡 🆘 Provide additional support and scaffolding for Multiplication\n", "  Division        🔴 Struggling   (Score: 0.223)\n", "    💡 🆘 Provide additional support and scaffolding for Division\n", "\n", "📊 Student B Mastery Analysis:\n", "------------------------------\n", "  Addition        🟠 Developing   (Score: 0.501)\n", "    💡 📖 Review Addition fundamentals and practice\n", "  Subtraction     🟠 Developing   (Score: 0.415)\n", "    💡 📖 Review Subtraction fundamentals and practice\n", "  Multiplication  🟠 Developing   (Score: 0.434)\n", "    💡 📖 Review Multiplication fundamentals and practice\n", "  Division        🟠 Developing   (Score: 0.498)\n", "    💡 📖 Review Division fundamentals and practice\n", "\n", "📊 Student C Mastery Analysis:\n", "------------------------------\n", "  Addition        🔴 Struggling   (Score: 0.254)\n", "    💡 🆘 Provide additional support and scaffolding for Addition\n", "  Subtraction     🔴 Struggling   (Score: 0.223)\n", "    💡 🆘 Provide additional support and scaffolding for Subtraction\n", "  Multiplication  🔴 Struggling   (Score: 0.290)\n", "    💡 🆘 Provide additional support and scaffolding for Multiplication\n", "  Division        🔴 Struggling   (Score: 0.202)\n", "    💡 🆘 Provide additional support and scaffolding for Division\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📈 Mastery Distribution:\n", "  Developing  :  6 ( 50.0%)\n", "  Struggling  :  6 ( 50.0%)\n"]}], "source": ["# Learning mastery analysis\n", "print(\"🎯 Learning Mastery State Analysis\")\n", "print(\"=\" * 40)\n", "\n", "def analyze_mastery_state(prediction_score):\n", "    \"\"\"Analyze learning mastery based on prediction score\"\"\"\n", "    if prediction_score >= 0.8:\n", "        return \"Mastered\", \"🟢\"\n", "    elif prediction_score >= 0.6:\n", "        return \"Proficient\", \"🟡\"\n", "    elif prediction_score >= 0.4:\n", "        return \"Developing\", \"🟠\"\n", "    else:\n", "        return \"Struggling\", \"🔴\"\n", "\n", "def get_learning_recommendations(mastery_state, skill):\n", "    \"\"\"Get learning recommendations based on mastery state\"\"\"\n", "    recommendations = {\n", "        \"Mastered\": f\"✅ Move to advanced {skill} topics or related skills\",\n", "        \"Proficient\": f\"📚 Practice more complex {skill} problems\",\n", "        \"Developing\": f\"📖 Review {skill} fundamentals and practice\",\n", "        \"Struggling\": f\"🆘 Provide additional support and scaffolding for {skill}\"\n", "    }\n", "    return recommendations.get(mastery_state, \"Continue practicing\")\n", "\n", "# Analyze different student profiles\n", "student_profiles = {\n", "    \"Student A\": {\n", "        \"Addition\": [1, 1, 1, 1, 1],\n", "        \"Subtraction\": [1, 0, 1, 1, 1],\n", "        \"Multiplication\": [0, 1, 0, 1, 0],\n", "        \"Division\": [0, 0, 1, 0, 0]\n", "    },\n", "    \"Student B\": {\n", "        \"Addition\": [1, 1, 0, 1, 1],\n", "        \"Subtraction\": [0, 1, 1, 0, 1],\n", "        \"Multiplication\": [1, 1, 1, 1, 0],\n", "        \"Division\": [1, 0, 1, 1, 1]\n", "    },\n", "    \"Student C\": {\n", "        \"Addition\": [0, 0, 0, 1, 0],\n", "        \"Subtraction\": [0, 0, 1, 0, 0],\n", "        \"Multiplication\": [0, 0, 0, 0, 1],\n", "        \"Division\": [0, 0, 0, 0, 0]\n", "    }\n", "}\n", "\n", "# Use the best available model for analysis\n", "best_model = 'ensemble' if 'ensemble' in loaded_models else loaded_models[0]\n", "print(f\"Using {best_model.upper()} model for mastery analysis\\n\")\n", "\n", "mastery_results = []\n", "\n", "for student_name, skills_data in student_profiles.items():\n", "    print(f\"📊 {student_name} Mastery Analysis:\")\n", "    print(\"-\" * 30)\n", "    \n", "    for skill, history in skills_data.items():\n", "        try:\n", "            prediction = predictor.predict_student_performance(\n", "                user_history=history,\n", "                skill=skill,\n", "                model_type=best_model\n", "            )\n", "            \n", "            mastery_state, emoji = analyze_mastery_state(prediction)\n", "            recommendation = get_learning_recommendations(mastery_state, skill)\n", "            \n", "            print(f\"  {skill:15} {emoji} {mastery_state:12} (Score: {prediction:.3f})\")\n", "            print(f\"    💡 {recommendation}\")\n", "            \n", "            mastery_results.append({\n", "                'Student': student_name,\n", "                'Skill': skill,\n", "                'Prediction': prediction,\n", "                'Mastery_State': mastery_state,\n", "                'Recent_Performance': np.mean(history[-3:])\n", "            })\n", "            \n", "        except Exception as e:\n", "            print(f\"  {skill:15} ❌ Error: {e}\")\n", "    \n", "    print()\n", "\n", "# Create mastery visualization\n", "if mastery_results:\n", "    mastery_df = pd.DataFrame(mastery_results)\n", "    \n", "    # Create heatmap of mastery states\n", "    pivot_df = mastery_df.pivot(index='Student', columns='Skill', values='Prediction')\n", "    \n", "    plt.figure(figsize=(10, 6))\n", "    sns.heatmap(pivot_df, annot=True, cmap='RdYlGn', center=0.5, \n", "                fmt='.3f', cbar_kws={'label': 'Predicted Success Probability'})\n", "    plt.title('Student Mastery Heatmap', fontsize=14, fontweight='bold')\n", "    plt.xlabel('Skills')\n", "    plt.ylabel('Students')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"\\n📈 Mastery Distribution:\")\n", "    mastery_counts = mastery_df['Mastery_State'].value_counts()\n", "    for state, count in mastery_counts.items():\n", "        percentage = (count / len(mastery_df)) * 100\n", "        print(f\"  {state:12}: {count:2d} ({percentage:5.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Real-Time Learning Simulation"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Real-Time Learning Simulation\n", "========================================\n", "🎯 Simulating learning session for 'Addition' using ENSEMBLE model\n", "Initial history: [0, 1, 0, 1, 0]\n", "Initial performance: 0.40\n", "\n", "Interaction  1: ✅ Correct | Prediction: 0.318 | State: 🔴 Struggling\n", "Interaction  2: ✅ Correct | Prediction: 0.387 | State: 🔴 Struggling\n", "Interaction  3: ❌ Incorrect | Prediction: 0.430 | State: 🟠 Developing\n", "Interaction  4: ❌ Incorrect | Prediction: 0.360 | State: 🔴 Struggling\n", "              💡 Suggestion: Provide additional scaffolding\n", "Interaction  5: ✅ Correct | Prediction: 0.313 | State: 🔴 Struggling\n", "              💡 Suggestion: Provide additional scaffolding\n", "Interaction  6: ❌ Incorrect | Prediction: 0.391 | State: 🔴 Struggling\n", "              💡 Suggestion: Provide additional scaffolding\n", "Interaction  7: ❌ Incorrect | Prediction: 0.334 | State: 🔴 Struggling\n", "              💡 Suggestion: Provide additional scaffolding\n", "Interaction  8: ✅ Correct | Prediction: 0.282 | State: 🔴 Struggling\n", "              💡 Suggestion: Provide additional scaffolding\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Session Summary:\n", "Initial prediction: 0.318\n", "Final prediction: 0.282\n", "Improvement: -0.036\n", "Session accuracy: 0.50\n", "Total interactions: 8\n", "Final mastery state: 🔴 Struggling\n"]}], "source": ["# Real-time learning simulation\n", "print(\"🔄 Real-Time Learning Simulation\")\n", "print(\"=\" * 40)\n", "\n", "def simulate_learning_session(predictor, initial_history, skill, model_type, n_interactions=10):\n", "    \"\"\"Simulate a real-time learning session\"\"\"\n", "    history = initial_history.copy()\n", "    predictions = []\n", "    interactions = []\n", "    \n", "    print(f\"🎯 Simulating learning session for '{skill}' using {model_type.upper()} model\")\n", "    print(f\"Initial history: {initial_history}\")\n", "    print(f\"Initial performance: {np.mean(initial_history):.2f}\\n\")\n", "    \n", "    for i in range(n_interactions):\n", "        # Get current prediction\n", "        try:\n", "            current_prediction = predictor.predict_student_performance(\n", "                user_history=history,\n", "                skill=skill,\n", "                model_type=model_type\n", "            )\n", "            predictions.append(current_prediction)\n", "            \n", "            # Simulate student response based on prediction + some randomness\n", "            # Higher prediction = higher chance of correct response\n", "            response_prob = current_prediction * 0.8 + np.random.normal(0, 0.1)\n", "            response_prob = max(0.1, min(0.9, response_prob))  # Clamp between 0.1 and 0.9\n", "            \n", "            student_response = 1 if np.random.random() < response_prob else 0\n", "            history.append(student_response)\n", "            interactions.append(student_response)\n", "            \n", "            # Real-time feedback\n", "            status = \"✅ Correct\" if student_response == 1 else \"❌ Incorrect\"\n", "            mastery_state, emoji = analyze_mastery_state(current_prediction)\n", "            \n", "            print(f\"Interaction {i+1:2d}: {status} | Prediction: {current_prediction:.3f} | State: {emoji} {mastery_state}\")\n", "            \n", "            # Adaptive feedback\n", "            if current_prediction < 0.4 and i > 2:\n", "                print(f\"              💡 Suggestion: Provide additional scaffolding\")\n", "            elif current_prediction > 0.8:\n", "                print(f\"              🚀 Suggestion: Increase difficulty level\")\n", "                \n", "        except Exception as e:\n", "            print(f\"Interaction {i+1:2d}: Error - {e}\")\n", "            predictions.append(0.5)\n", "            interactions.append(0)\n", "    \n", "    return history, predictions, interactions\n", "\n", "# Run simulation\n", "if loaded_models:\n", "    initial_student_history = [0, 1, 0, 1, 0]  # Mixed performance\n", "    target_skill = \"Addition\"\n", "    model_to_use = 'ensemble' if 'ensemble' in loaded_models else loaded_models[0]\n", "    \n", "    final_history, prediction_trajectory, new_interactions = simulate_learning_session(\n", "        predictor=predictor,\n", "        initial_history=initial_student_history,\n", "        skill=target_skill,\n", "        model_type=model_to_use,\n", "        n_interactions=8\n", "    )\n", "    \n", "    # Visualize learning trajectory\n", "    plt.figure(figsize=(12, 8))\n", "    \n", "    # Plot 1: Prediction trajectory\n", "    plt.subplot(2, 1, 1)\n", "    interaction_numbers = range(1, len(prediction_trajectory) + 1)\n", "    plt.plot(interaction_numbers, prediction_trajectory, 'b-o', linewidth=2, markersize=6)\n", "    plt.axhline(y=0.8, color='g', linestyle='--', alpha=0.7, label='Mastery Threshold')\n", "    plt.axhline(y=0.6, color='orange', linestyle='--', alpha=0.7, label='Proficiency Threshold')\n", "    plt.axhline(y=0.4, color='r', linestyle='--', alpha=0.7, label='Struggling Threshold')\n", "    plt.xlabel('Interaction Number')\n", "    plt.ylabel('Predicted Success Probability')\n", "    plt.title(f'Learning Trajectory for {target_skill} ({model_to_use.upper()} Model)', fontweight='bold')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    plt.ylim(0, 1)\n", "    \n", "    # Plot 2: Student responses\n", "    plt.subplot(2, 1, 2)\n", "    colors = ['red' if x == 0 else 'green' for x in new_interactions]\n", "    plt.bar(interaction_numbers, new_interactions, color=colors, alpha=0.7)\n", "    plt.xlabel('Interaction Number')\n", "    plt.ylabel('Student Response (1=Correct, 0=Incorrect)')\n", "    plt.title('Student Responses During Session', fontweight='bold')\n", "    plt.ylim(-0.1, 1.1)\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Session summary\n", "    print(f\"\\n📊 Session Summary:\")\n", "    print(f\"Initial prediction: {prediction_trajectory[0]:.3f}\")\n", "    print(f\"Final prediction: {prediction_trajectory[-1]:.3f}\")\n", "    print(f\"Improvement: {prediction_trajectory[-1] - prediction_trajectory[0]:+.3f}\")\n", "    print(f\"Session accuracy: {np.mean(new_interactions):.2f}\")\n", "    print(f\"Total interactions: {len(new_interactions)}\")\n", "    \n", "    final_mastery, final_emoji = analyze_mastery_state(prediction_trajectory[-1])\n", "    print(f\"Final mastery state: {final_emoji} {final_mastery}\")\n", "else:\n", "    print(\"No models available for simulation.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Production Integration Example"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🏭 Production Integration Example\n", "========================================\n", "\n", "🔄 Simulating Real-Time System:\n", "-----------------------------------\n", "📚 Started session for Student student_123\n", "  Addition     ✅ Correct | Prediction: 0.270 | 🔴 Struggling\n", "               💡 🆘 Provide immediate support for Addition\n", "  Addition     ❌ Incorrect | Prediction: 0.270 | 🔴 Struggling\n", "               💡 🆘 Provide immediate support for Addition\n", "  Addition     ✅ Correct | Prediction: 0.270 | 🔴 Struggling\n", "               💡 🆘 Provide immediate support for Addition\n", "  Subtraction  ✅ Correct | Prediction: 0.270 | 🔴 Struggling\n", "               💡 🆘 Provide immediate support for Subtraction\n", "  Subtraction  ✅ Correct | Prediction: 0.270 | 🔴 Struggling\n", "               💡 🆘 Provide immediate support for Subtraction\n", "\n", "📊 Session Summary for Student 123:\n", "  Duration: 0.0 minutes\n", "  Interactions: 5\n", "  Accuracy: 0.80\n", "  Final Prediction: 0.270\n", "  Improvement: +0.000\n", "\n", "✅ Prediction demo completed successfully!\n", "\n", "💡 This system can be integrated into:\n", "   - Adaptive learning platforms\n", "   - Intelligent tutoring systems\n", "   - Educational assessment tools\n", "   - Learning analytics dashboards\n"]}], "source": ["# Production integration example\n", "print(\"🏭 Production Integration Example\")\n", "print(\"=\" * 40)\n", "\n", "class RealTimeKTSystem:\n", "    \"\"\"Example of how to integrate KT prediction in a real-time system\"\"\"\n", "    \n", "    def __init__(self, predictor, model_type='ensemble'):\n", "        self.predictor = predictor\n", "        self.model_type = model_type\n", "        self.student_sessions = {}\n", "    \n", "    def start_session(self, student_id, initial_history=None):\n", "        \"\"\"Start a new learning session for a student\"\"\"\n", "        self.student_sessions[student_id] = {\n", "            'history': initial_history or [],\n", "            'start_time': datetime.now(),\n", "            'interactions': 0,\n", "            'predictions': []\n", "        }\n", "        print(f\"📚 Started session for Student {student_id}\")\n", "    \n", "    def record_interaction(self, student_id, skill, response):\n", "        \"\"\"Record a student interaction and get real-time prediction\"\"\"\n", "        if student_id not in self.student_sessions:\n", "            self.start_session(student_id)\n", "        \n", "        session = self.student_sessions[student_id]\n", "        session['history'].append(response)\n", "        session['interactions'] += 1\n", "        \n", "        # Get prediction\n", "        try:\n", "            prediction = self.predictor.predict_student_performance(\n", "                user_history=session['history'],\n", "                skill=skill,\n", "                model_type=self.model_type\n", "            )\n", "            session['predictions'].append(prediction)\n", "            \n", "            # Generate adaptive recommendations\n", "            mastery_state, emoji = analyze_mastery_state(prediction)\n", "            recommendation = self.get_adaptive_recommendation(prediction, skill, session)\n", "            \n", "            return {\n", "                'prediction': prediction,\n", "                'mastery_state': mastery_state,\n", "                'emoji': emoji,\n", "                'recommendation': recommendation,\n", "                'session_progress': session['interactions']\n", "            }\n", "            \n", "        except Exception as e:\n", "            return {'error': str(e)}\n", "    \n", "    def get_adaptive_recommendation(self, prediction, skill, session):\n", "        \"\"\"Generate adaptive learning recommendations\"\"\"\n", "        recent_performance = np.mean(session['history'][-3:]) if len(session['history']) >= 3 else np.mean(session['history'])\n", "        \n", "        if prediction >= 0.8:\n", "            return f\"🚀 Ready for advanced {skill} challenges\"\n", "        elif prediction >= 0.6:\n", "            return f\"📚 Continue with current {skill} difficulty\"\n", "        elif prediction >= 0.4:\n", "            return f\"📖 Review {skill} concepts before proceeding\"\n", "        else:\n", "            return f\"🆘 Provide immediate support for {skill}\"\n", "    \n", "    def get_session_summary(self, student_id):\n", "        \"\"\"Get summary of student session\"\"\"\n", "        if student_id not in self.student_sessions:\n", "            return None\n", "        \n", "        session = self.student_sessions[student_id]\n", "        duration = datetime.now() - session['start_time']\n", "        \n", "        return {\n", "            'duration_minutes': duration.total_seconds() / 60,\n", "            'total_interactions': session['interactions'],\n", "            'accuracy': np.mean(session['history']) if session['history'] else 0,\n", "            'final_prediction': session['predictions'][-1] if session['predictions'] else None,\n", "            'improvement': session['predictions'][-1] - session['predictions'][0] if len(session['predictions']) > 1 else 0\n", "        }\n", "\n", "# Demo the real-time system\n", "if loaded_models:\n", "    rt_system = RealTimeKTSystem(predictor, model_type=loaded_models[0])\n", "    \n", "    # Simulate real-time interactions\n", "    print(\"\\n🔄 Simulating Real-Time System:\")\n", "    print(\"-\" * 35)\n", "    \n", "    # Student interactions\n", "    interactions = [\n", "        (\"student_123\", \"Addition\", 1),\n", "        (\"student_123\", \"Addition\", 0),\n", "        (\"student_123\", \"Addition\", 1),\n", "        (\"student_123\", \"Subtraction\", 1),\n", "        (\"student_123\", \"Subtraction\", 1),\n", "    ]\n", "    \n", "    for student_id, skill, response in interactions:\n", "        result = rt_system.record_interaction(student_id, skill, response)\n", "        \n", "        if 'error' not in result:\n", "            status = \"✅ Correct\" if response == 1 else \"❌ Incorrect\"\n", "            print(f\"  {skill:12} {status} | Prediction: {result['prediction']:.3f} | {result['emoji']} {result['mastery_state']}\")\n", "            print(f\"               💡 {result['recommendation']}\")\n", "        else:\n", "            print(f\"  Error: {result['error']}\")\n", "    \n", "    # Session summary\n", "    summary = rt_system.get_session_summary(\"student_123\")\n", "    if summary:\n", "        print(f\"\\n📊 Session Summary for Student 123:\")\n", "        print(f\"  Duration: {summary['duration_minutes']:.1f} minutes\")\n", "        print(f\"  Interactions: {summary['total_interactions']}\")\n", "        print(f\"  Accuracy: {summary['accuracy']:.2f}\")\n", "        print(f\"  Final Prediction: {summary['final_prediction']:.3f}\")\n", "        print(f\"  Improvement: {summary['improvement']:+.3f}\")\n", "else:\n", "    print(\"No models available for real-time system demo.\")\n", "\n", "print(\"\\n✅ Prediction demo completed successfully!\")\n", "print(\"\\n💡 This system can be integrated into:\")\n", "print(\"   - Adaptive learning platforms\")\n", "print(\"   - Intelligent tutoring systems\")\n", "print(\"   - Educational assessment tools\")\n", "print(\"   - Learning analytics dashboards\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}