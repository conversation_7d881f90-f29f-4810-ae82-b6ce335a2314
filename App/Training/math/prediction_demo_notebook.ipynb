{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Knowledge Tracing Prediction Demo\n", "\n", "This notebook demonstrates how to use the knowledge tracing prediction system to predict student knowledge states and analyze learning mastery. It showcases real-time prediction capabilities for adaptive learning applications.\n", "\n", "## Features\n", "- Load and use trained KT models (BKT, PFA, DKT, Ensemble)\n", "- Predict student performance for specific skills\n", "- Analyze learning mastery states\n", "- Compare different model predictions\n", "- Simulate real-time learning scenarios\n", "\n", "## Use Cases\n", "- Real-time student assessment\n", "- Adaptive learning path recommendations\n", "- Learning analytics and progress tracking\n", "- Educational intervention triggers"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 17:33:10.940073: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📚 Libraries imported successfully!\n", "Current working directory: /home/<USER>/workspace/AClass/App/Training/math\n"]}], "source": ["# Import required libraries\n", "import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add pipeline directory to path\n", "sys.path.append('../pipeline')\n", "\n", "# Import prediction module\n", "from prediction import KTPredictor\n", "\n", "# Set up plotting\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"📚 Libraries imported successfully!\")\n", "print(f\"Current working directory: {os.getcwd()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Initialize Prediction System"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Initializing Knowledge Tracing Predictor...\n", "\n", "📊 Loading trained models...\n", "\n", "✅ Models loaded successfully!\n", "Available models: ['bkt', 'pfa', 'dkt', 'ensemble']\n", "\n", "🎯 Ready for prediction with 4 models!\n"]}], "source": ["# Initialize the knowledge tracing predictor\n", "print(\"🚀 Initializing Knowledge Tracing Predictor...\")\n", "predictor = KTPredictor(models_dir=\"../models/outputs\")\n", "\n", "# Load trained models\n", "print(\"\\n📊 Loading trained models...\")\n", "load_status = predictor.load_models()\n", "\n", "print(f\"\\n✅ Models loaded successfully!\")\n", "loaded_models = [model for model, status in load_status.items() if status]\n", "print(f\"Available models: {loaded_models}\")\n", "\n", "if not loaded_models:\n", "    print(\"⚠️ No models loaded. Please ensure models are trained and saved.\")\n", "else:\n", "    print(f\"\\n🎯 Ready for prediction with {len(loaded_models)} models!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Basic Prediction Examples"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📈 Example 1: Single Prediction\n", "========================================\n", "Student history: [1, 0, 1, 1, 0, 1]\n", "Target skill: Addition\n", "Recent performance: 0.67\n", "\n", "Predictions from different models:\n", "  BKT         : 0.270\n", "  PFA         : 0.600\n", "  DKT         : 0.671\n", "  ENSEMBLE    : 0.423\n"]}], "source": ["# Example 1: Single prediction\n", "print(\"📈 Example 1: Single Prediction\")\n", "print(\"=\" * 40)\n", "\n", "# Student interaction history (1=correct, 0=incorrect)\n", "user_history = [1, 0, 1, 1, 0, 1]\n", "target_skill = \"Addition\"\n", "\n", "print(f\"Student history: {user_history}\")\n", "print(f\"Target skill: {target_skill}\")\n", "print(f\"Recent performance: {np.mean(user_history[-3:]):.2f}\")\n", "\n", "# Get predictions from all available models\n", "print(\"\\nPredictions from different models:\")\n", "for model_name in loaded_models:\n", "    try:\n", "        prediction = predictor.predict_student_performance(\n", "            user_history=user_history,\n", "            skill=target_skill,\n", "            model_type=model_name\n", "        )\n", "        print(f\"  {model_name.upper():12}: {prediction:.3f}\")\n", "    except Exception as e:\n", "        print(f\"  {model_name.upper():12}: Error - {e}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Example 2: Model Comparison Visualization\n", "=============================================\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📋 Summary Statistics:\n", "                              mean  std\n", "<PERSON><PERSON>                      \n", "BKT      Average Student     0.270  0.0\n", "         High Performer      0.270  0.0\n", "         Improving Student   0.270  0.0\n", "         Struggling Student  0.270  0.0\n", "DKT      Average Student     0.671  0.0\n", "         High Performer      0.788  0.0\n", "         Improving Student   0.646  0.0\n", "         Struggling Student  0.452  0.0\n", "ENSEMBLE Average Student     0.423  0.0\n", "         High Performer      0.478  0.0\n", "         Improving Student   0.416  0.0\n", "         Struggling Student  0.338  0.0\n", "PFA      Average Student     0.600  0.0\n", "         High Performer      0.800  0.0\n", "         Improving Student   0.600  0.0\n", "         Struggling Student  0.400  0.0\n"]}], "source": ["# Example 2: Model comparison visualization\n", "print(\"\\n📊 Example 2: Model Comparison Visualization\")\n", "print(\"=\" * 45)\n", "\n", "# Test different scenarios\n", "scenarios = {\n", "    \"Struggling Student\": [0, 0, 1, 0, 0, 1],\n", "    \"Average Student\": [1, 0, 1, 1, 0, 1],\n", "    \"High Performer\": [1, 1, 1, 1, 0, 1],\n", "    \"Improving Student\": [0, 0, 0, 1, 1, 1]\n", "}\n", "\n", "skills = [\"Addition\", \"Subtraction\", \"Multiplication\", \"Division\"]\n", "\n", "# Create comparison data\n", "comparison_data = []\n", "for scenario_name, history in scenarios.items():\n", "    for skill in skills:\n", "        for model_name in loaded_models:\n", "            try:\n", "                prediction = predictor.predict_student_performance(\n", "                    user_history=history,\n", "                    skill=skill,\n", "                    model_type=model_name\n", "                )\n", "                comparison_data.append({\n", "                    'Scenario': scenario_name,\n", "                    'Skill': skill,\n", "                    'Model': model_name.upper(),\n", "                    'Prediction': prediction,\n", "                    'Recent_Performance': np.mean(history[-3:])\n", "                })\n", "            except Exception as e:\n", "                print(f\"Error with {model_name} for {scenario_name}-{skill}: {e}\")\n", "\n", "# Convert to DataFrame\n", "comparison_df = pd.DataFrame(comparison_data)\n", "\n", "if not comparison_df.empty:\n", "    # Create visualization\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    axes = axes.flatten()\n", "    \n", "    for i, skill in enumerate(skills):\n", "        skill_data = comparison_df[comparison_df['Skill'] == skill]\n", "        if not skill_data.empty:\n", "            sns.barplot(data=skill_data, x='Scenario', y='Prediction', hue='Model', ax=axes[i])\n", "            axes[i].set_title(f'{skill} - Model Predictions', fontweight='bold')\n", "            axes[i].set_xlabel('Student Scenario')\n", "            axes[i].set_ylabel('Predicted Success Probability')\n", "            axes[i].tick_params(axis='x', rotation=45)\n", "            axes[i].legend(title='Model')\n", "            axes[i].set_ylim(0, 1)\n", "    \n", "    plt.tight_layout()\n", "    plt.suptitle('Knowledge Tracing Model Predictions Comparison', fontsize=16, fontweight='bold', y=1.02)\n", "    plt.show()\n", "    \n", "    print(\"\\n📋 Summary Statistics:\")\n", "    print(comparison_df.groupby(['Model', 'Sc<PERSON><PERSON>'])['Prediction'].agg(['mean', 'std']).round(3))\n", "else:\n", "    print(\"No comparison data available.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Learning Mastery State Analysis"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 Learning Mastery State Analysis\n", "========================================\n", "Using ENSEMBLE model for mastery analysis\n", "\n", "📊 Student A Mastery Analysis:\n", "------------------------------\n", "  Addition        🟠 Developing   (Score: 0.522)\n", "    💡 📖 Review Addition fundamentals and practice\n", "  Subtraction     🟠 Developing   (Score: 0.498)\n", "    💡 📖 Review Subtraction fundamentals and practice\n", "  Multiplication  🔴 Struggling   (Score: 0.318)\n", "    💡 🆘 Provide additional support and scaffolding for Multiplication\n", "  Division        🔴 Struggling   (Score: 0.223)\n", "    💡 🆘 Provide additional support and scaffolding for Division\n", "\n", "📊 Student B Mastery Analysis:\n", "------------------------------\n", "  Addition        🟠 Developing   (Score: 0.501)\n", "    💡 📖 Review Addition fundamentals and practice\n", "  Subtraction     🟠 Developing   (Score: 0.415)\n", "    💡 📖 Review Subtraction fundamentals and practice\n", "  Multiplication  🟠 Developing   (Score: 0.434)\n", "    💡 📖 Review Multiplication fundamentals and practice\n", "  Division        🟠 Developing   (Score: 0.498)\n", "    💡 📖 Review Division fundamentals and practice\n", "\n", "📊 Student C Mastery Analysis:\n", "------------------------------\n", "  Addition        🔴 Struggling   (Score: 0.254)\n", "    💡 🆘 Provide additional support and scaffolding for Addition\n", "  Subtraction     🔴 Struggling   (Score: 0.223)\n", "    💡 🆘 Provide additional support and scaffolding for Subtraction\n", "  Multiplication  🔴 Struggling   (Score: 0.290)\n", "    💡 🆘 Provide additional support and scaffolding for Multiplication\n", "  Division        🔴 Struggling   (Score: 0.202)\n", "    💡 🆘 Provide additional support and scaffolding for Division\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📈 Mastery Distribution:\n", "  Developing  :  6 ( 50.0%)\n", "  Struggling  :  6 ( 50.0%)\n"]}], "source": ["# Learning mastery analysis\n", "print(\"🎯 Learning Mastery State Analysis\")\n", "print(\"=\" * 40)\n", "\n", "def analyze_mastery_state(prediction_score):\n", "    \"\"\"Analyze learning mastery based on prediction score\"\"\"\n", "    if prediction_score >= 0.8:\n", "        return \"Mastered\", \"🟢\"\n", "    elif prediction_score >= 0.6:\n", "        return \"Proficient\", \"🟡\"\n", "    elif prediction_score >= 0.4:\n", "        return \"Developing\", \"🟠\"\n", "    else:\n", "        return \"Struggling\", \"🔴\"\n", "\n", "def get_learning_recommendations(mastery_state, skill):\n", "    \"\"\"Get learning recommendations based on mastery state\"\"\"\n", "    recommendations = {\n", "        \"Mastered\": f\"✅ Move to advanced {skill} topics or related skills\",\n", "        \"Proficient\": f\"📚 Practice more complex {skill} problems\",\n", "        \"Developing\": f\"📖 Review {skill} fundamentals and practice\",\n", "        \"Struggling\": f\"🆘 Provide additional support and scaffolding for {skill}\"\n", "    }\n", "    return recommendations.get(mastery_state, \"Continue practicing\")\n", "\n", "# Analyze different student profiles\n", "student_profiles = {\n", "    \"Student A\": {\n", "        \"Addition\": [1, 1, 1, 1, 1],\n", "        \"Subtraction\": [1, 0, 1, 1, 1],\n", "        \"Multiplication\": [0, 1, 0, 1, 0],\n", "        \"Division\": [0, 0, 1, 0, 0]\n", "    },\n", "    \"Student B\": {\n", "        \"Addition\": [1, 1, 0, 1, 1],\n", "        \"Subtraction\": [0, 1, 1, 0, 1],\n", "        \"Multiplication\": [1, 1, 1, 1, 0],\n", "        \"Division\": [1, 0, 1, 1, 1]\n", "    },\n", "    \"Student C\": {\n", "        \"Addition\": [0, 0, 0, 1, 0],\n", "        \"Subtraction\": [0, 0, 1, 0, 0],\n", "        \"Multiplication\": [0, 0, 0, 0, 1],\n", "        \"Division\": [0, 0, 0, 0, 0]\n", "    }\n", "}\n", "\n", "# Use the best available model for analysis\n", "best_model = 'ensemble' if 'ensemble' in loaded_models else loaded_models[0]\n", "print(f\"Using {best_model.upper()} model for mastery analysis\\n\")\n", "\n", "mastery_results = []\n", "\n", "for student_name, skills_data in student_profiles.items():\n", "    print(f\"📊 {student_name} Mastery Analysis:\")\n", "    print(\"-\" * 30)\n", "    \n", "    for skill, history in skills_data.items():\n", "        try:\n", "            prediction = predictor.predict_student_performance(\n", "                user_history=history,\n", "                skill=skill,\n", "                model_type=best_model\n", "            )\n", "            \n", "            mastery_state, emoji = analyze_mastery_state(prediction)\n", "            recommendation = get_learning_recommendations(mastery_state, skill)\n", "            \n", "            print(f\"  {skill:15} {emoji} {mastery_state:12} (Score: {prediction:.3f})\")\n", "            print(f\"    💡 {recommendation}\")\n", "            \n", "            mastery_results.append({\n", "                'Student': student_name,\n", "                'Skill': skill,\n", "                'Prediction': prediction,\n", "                'Mastery_State': mastery_state,\n", "                'Recent_Performance': np.mean(history[-3:])\n", "            })\n", "            \n", "        except Exception as e:\n", "            print(f\"  {skill:15} ❌ Error: {e}\")\n", "    \n", "    print()\n", "\n", "# Create mastery visualization\n", "if mastery_results:\n", "    mastery_df = pd.DataFrame(mastery_results)\n", "    \n", "    # Create heatmap of mastery states\n", "    pivot_df = mastery_df.pivot(index='Student', columns='Skill', values='Prediction')\n", "    \n", "    plt.figure(figsize=(10, 6))\n", "    sns.heatmap(pivot_df, annot=True, cmap='RdYlGn', center=0.5, \n", "                fmt='.3f', cbar_kws={'label': 'Predicted Success Probability'})\n", "    plt.title('Student Mastery Heatmap', fontsize=14, fontweight='bold')\n", "    plt.xlabel('Skills')\n", "    plt.ylabel('Students')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"\\n📈 Mastery Distribution:\")\n", "    mastery_counts = mastery_df['Mastery_State'].value_counts()\n", "    for state, count in mastery_counts.items():\n", "        percentage = (count / len(mastery_df)) * 100\n", "        print(f\"  {state:12}: {count:2d} ({percentage:5.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Real-Time Learning Simulation"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Real-Time Learning Simulation\n", "========================================\n", "🎯 Simulating learning session for 'Addition' using ENSEMBLE model\n", "Initial history: [0, 1, 0, 1, 0]\n", "Initial performance: 0.40\n", "\n", "Interaction  1: ❌ Incorrect | Prediction: 0.318 | State: 🔴 Struggling\n", "Interaction  2: ❌ Incorrect | Prediction: 0.274 | State: 🔴 Struggling\n", "Interaction  3: ✅ Correct | Prediction: 0.252 | State: 🔴 Struggling\n", "Interaction  4: ❌ Incorrect | Prediction: 0.334 | State: 🔴 Struggling\n", "              💡 Suggestion: Provide additional scaffolding\n", "Interaction  5: ❌ Incorrect | Prediction: 0.278 | State: 🔴 Struggling\n", "              💡 Suggestion: Provide additional scaffolding\n", "Interaction  6: ✅ Correct | Prediction: 0.248 | State: 🔴 Struggling\n", "              💡 Suggestion: Provide additional scaffolding\n", "Interaction  7: ❌ Incorrect | Prediction: 0.330 | State: 🔴 Struggling\n", "              💡 Suggestion: Provide additional scaffolding\n", "Interaction  8: ❌ Incorrect | Prediction: 0.300 | State: 🔴 Struggling\n", "              💡 Suggestion: Provide additional scaffolding\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Session Summary:\n", "Initial prediction: 0.318\n", "Final prediction: 0.300\n", "Improvement: -0.018\n", "Session accuracy: 0.25\n", "Total interactions: 8\n", "Final mastery state: 🔴 Struggling\n"]}], "source": ["# Real-time learning simulation\n", "print(\"🔄 Real-Time Learning Simulation\")\n", "print(\"=\" * 40)\n", "\n", "def simulate_learning_session(predictor, initial_history, skill, model_type, n_interactions=10):\n", "    \"\"\"Simulate a real-time learning session\"\"\"\n", "    history = initial_history.copy()\n", "    predictions = []\n", "    interactions = []\n", "    \n", "    print(f\"🎯 Simulating learning session for '{skill}' using {model_type.upper()} model\")\n", "    print(f\"Initial history: {initial_history}\")\n", "    print(f\"Initial performance: {np.mean(initial_history):.2f}\\n\")\n", "    \n", "    for i in range(n_interactions):\n", "        # Get current prediction\n", "        try:\n", "            current_prediction = predictor.predict_student_performance(\n", "                user_history=history,\n", "                skill=skill,\n", "                model_type=model_type\n", "            )\n", "            predictions.append(current_prediction)\n", "            \n", "            # Simulate student response based on prediction + some randomness\n", "            # Higher prediction = higher chance of correct response\n", "            response_prob = current_prediction * 0.8 + np.random.normal(0, 0.1)\n", "            response_prob = max(0.1, min(0.9, response_prob))  # Clamp between 0.1 and 0.9\n", "            \n", "            student_response = 1 if np.random.random() < response_prob else 0\n", "            history.append(student_response)\n", "            interactions.append(student_response)\n", "            \n", "            # Real-time feedback\n", "            status = \"✅ Correct\" if student_response == 1 else \"❌ Incorrect\"\n", "            mastery_state, emoji = analyze_mastery_state(current_prediction)\n", "            \n", "            print(f\"Interaction {i+1:2d}: {status} | Prediction: {current_prediction:.3f} | State: {emoji} {mastery_state}\")\n", "            \n", "            # Adaptive feedback\n", "            if current_prediction < 0.4 and i > 2:\n", "                print(f\"              💡 Suggestion: Provide additional scaffolding\")\n", "            elif current_prediction > 0.8:\n", "                print(f\"              🚀 Suggestion: Increase difficulty level\")\n", "                \n", "        except Exception as e:\n", "            print(f\"Interaction {i+1:2d}: Error - {e}\")\n", "            predictions.append(0.5)\n", "            interactions.append(0)\n", "    \n", "    return history, predictions, interactions\n", "\n", "# Run simulation\n", "if loaded_models:\n", "    initial_student_history = [0, 1, 0, 1, 0]  # Mixed performance\n", "    target_skill = \"Addition\"\n", "    model_to_use = 'ensemble' if 'ensemble' in loaded_models else loaded_models[0]\n", "    \n", "    final_history, prediction_trajectory, new_interactions = simulate_learning_session(\n", "        predictor=predictor,\n", "        initial_history=initial_student_history,\n", "        skill=target_skill,\n", "        model_type=model_to_use,\n", "        n_interactions=8\n", "    )\n", "    \n", "    # Visualize learning trajectory\n", "    plt.figure(figsize=(12, 8))\n", "    \n", "    # Plot 1: Prediction trajectory\n", "    plt.subplot(2, 1, 1)\n", "    interaction_numbers = range(1, len(prediction_trajectory) + 1)\n", "    plt.plot(interaction_numbers, prediction_trajectory, 'b-o', linewidth=2, markersize=6)\n", "    plt.axhline(y=0.8, color='g', linestyle='--', alpha=0.7, label='Mastery Threshold')\n", "    plt.axhline(y=0.6, color='orange', linestyle='--', alpha=0.7, label='Proficiency Threshold')\n", "    plt.axhline(y=0.4, color='r', linestyle='--', alpha=0.7, label='Struggling Threshold')\n", "    plt.xlabel('Interaction Number')\n", "    plt.ylabel('Predicted Success Probability')\n", "    plt.title(f'Learning Trajectory for {target_skill} ({model_to_use.upper()} Model)', fontweight='bold')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    plt.ylim(0, 1)\n", "    \n", "    # Plot 2: Student responses\n", "    plt.subplot(2, 1, 2)\n", "    colors = ['red' if x == 0 else 'green' for x in new_interactions]\n", "    plt.bar(interaction_numbers, new_interactions, color=colors, alpha=0.7)\n", "    plt.xlabel('Interaction Number')\n", "    plt.ylabel('Student Response (1=Correct, 0=Incorrect)')\n", "    plt.title('Student Responses During Session', fontweight='bold')\n", "    plt.ylim(-0.1, 1.1)\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Session summary\n", "    print(f\"\\n📊 Session Summary:\")\n", "    print(f\"Initial prediction: {prediction_trajectory[0]:.3f}\")\n", "    print(f\"Final prediction: {prediction_trajectory[-1]:.3f}\")\n", "    print(f\"Improvement: {prediction_trajectory[-1] - prediction_trajectory[0]:+.3f}\")\n", "    print(f\"Session accuracy: {np.mean(new_interactions):.2f}\")\n", "    print(f\"Total interactions: {len(new_interactions)}\")\n", "    \n", "    final_mastery, final_emoji = analyze_mastery_state(prediction_trajectory[-1])\n", "    print(f\"Final mastery state: {final_emoji} {final_mastery}\")\n", "else:\n", "    print(\"No models available for simulation.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Production Integration Example"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🏭 Production Integration Example\n", "========================================\n", "\n", "🔄 Simulating Real-Time System:\n", "-----------------------------------\n", "📚 Started session for Student student_123\n", "  Addition     ✅ Correct | Prediction: 0.270 | 🔴 Struggling\n", "               💡 🆘 Provide immediate support for Addition\n", "  Addition     ❌ Incorrect | Prediction: 0.270 | 🔴 Struggling\n", "               💡 🆘 Provide immediate support for Addition\n", "  Addition     ✅ Correct | Prediction: 0.270 | 🔴 Struggling\n", "               💡 🆘 Provide immediate support for Addition\n", "  Subtraction  ✅ Correct | Prediction: 0.270 | 🔴 Struggling\n", "               💡 🆘 Provide immediate support for Subtraction\n", "  Subtraction  ✅ Correct | Prediction: 0.270 | 🔴 Struggling\n", "               💡 🆘 Provide immediate support for Subtraction\n", "\n", "📊 Session Summary for Student 123:\n", "  Duration: 0.0 minutes\n", "  Interactions: 5\n", "  Accuracy: 0.80\n", "  Final Prediction: 0.270\n", "  Improvement: +0.000\n", "\n", "✅ Prediction demo completed successfully!\n", "\n", "💡 This system can be integrated into:\n", "   - Adaptive learning platforms\n", "   - Intelligent tutoring systems\n", "   - Educational assessment tools\n", "   - Learning analytics dashboards\n"]}], "source": ["# Production integration example\n", "print(\"🏭 Production Integration Example\")\n", "print(\"=\" * 40)\n", "\n", "class RealTimeKTSystem:\n", "    \"\"\"Example of how to integrate KT prediction in a real-time system\"\"\"\n", "    \n", "    def __init__(self, predictor, model_type='ensemble'):\n", "        self.predictor = predictor\n", "        self.model_type = model_type\n", "        self.student_sessions = {}\n", "    \n", "    def start_session(self, student_id, initial_history=None):\n", "        \"\"\"Start a new learning session for a student\"\"\"\n", "        self.student_sessions[student_id] = {\n", "            'history': initial_history or [],\n", "            'start_time': datetime.now(),\n", "            'interactions': 0,\n", "            'predictions': []\n", "        }\n", "        print(f\"📚 Started session for Student {student_id}\")\n", "    \n", "    def record_interaction(self, student_id, skill, response):\n", "        \"\"\"Record a student interaction and get real-time prediction\"\"\"\n", "        if student_id not in self.student_sessions:\n", "            self.start_session(student_id)\n", "        \n", "        session = self.student_sessions[student_id]\n", "        session['history'].append(response)\n", "        session['interactions'] += 1\n", "        \n", "        # Get prediction\n", "        try:\n", "            prediction = self.predictor.predict_student_performance(\n", "                user_history=session['history'],\n", "                skill=skill,\n", "                model_type=self.model_type\n", "            )\n", "            session['predictions'].append(prediction)\n", "            \n", "            # Generate adaptive recommendations\n", "            mastery_state, emoji = analyze_mastery_state(prediction)\n", "            recommendation = self.get_adaptive_recommendation(prediction, skill, session)\n", "            \n", "            return {\n", "                'prediction': prediction,\n", "                'mastery_state': mastery_state,\n", "                'emoji': emoji,\n", "                'recommendation': recommendation,\n", "                'session_progress': session['interactions']\n", "            }\n", "            \n", "        except Exception as e:\n", "            return {'error': str(e)}\n", "    \n", "    def get_adaptive_recommendation(self, prediction, skill, session):\n", "        \"\"\"Generate adaptive learning recommendations\"\"\"\n", "        recent_performance = np.mean(session['history'][-3:]) if len(session['history']) >= 3 else np.mean(session['history'])\n", "        \n", "        if prediction >= 0.8:\n", "            return f\"🚀 Ready for advanced {skill} challenges\"\n", "        elif prediction >= 0.6:\n", "            return f\"📚 Continue with current {skill} difficulty\"\n", "        elif prediction >= 0.4:\n", "            return f\"📖 Review {skill} concepts before proceeding\"\n", "        else:\n", "            return f\"🆘 Provide immediate support for {skill}\"\n", "    \n", "    def get_session_summary(self, student_id):\n", "        \"\"\"Get summary of student session\"\"\"\n", "        if student_id not in self.student_sessions:\n", "            return None\n", "        \n", "        session = self.student_sessions[student_id]\n", "        duration = datetime.now() - session['start_time']\n", "        \n", "        return {\n", "            'duration_minutes': duration.total_seconds() / 60,\n", "            'total_interactions': session['interactions'],\n", "            'accuracy': np.mean(session['history']) if session['history'] else 0,\n", "            'final_prediction': session['predictions'][-1] if session['predictions'] else None,\n", "            'improvement': session['predictions'][-1] - session['predictions'][0] if len(session['predictions']) > 1 else 0\n", "        }\n", "\n", "# Demo the real-time system\n", "if loaded_models:\n", "    rt_system = RealTimeKTSystem(predictor, model_type=loaded_models[0])\n", "    \n", "    # Simulate real-time interactions\n", "    print(\"\\n🔄 Simulating Real-Time System:\")\n", "    print(\"-\" * 35)\n", "    \n", "    # Student interactions\n", "    interactions = [\n", "        (\"student_123\", \"Addition\", 1),\n", "        (\"student_123\", \"Addition\", 0),\n", "        (\"student_123\", \"Addition\", 1),\n", "        (\"student_123\", \"Subtraction\", 1),\n", "        (\"student_123\", \"Subtraction\", 1),\n", "    ]\n", "    \n", "    for student_id, skill, response in interactions:\n", "        result = rt_system.record_interaction(student_id, skill, response)\n", "        \n", "        if 'error' not in result:\n", "            status = \"✅ Correct\" if response == 1 else \"❌ Incorrect\"\n", "            print(f\"  {skill:12} {status} | Prediction: {result['prediction']:.3f} | {result['emoji']} {result['mastery_state']}\")\n", "            print(f\"               💡 {result['recommendation']}\")\n", "        else:\n", "            print(f\"  Error: {result['error']}\")\n", "    \n", "    # Session summary\n", "    summary = rt_system.get_session_summary(\"student_123\")\n", "    if summary:\n", "        print(f\"\\n📊 Session Summary for Student 123:\")\n", "        print(f\"  Duration: {summary['duration_minutes']:.1f} minutes\")\n", "        print(f\"  Interactions: {summary['total_interactions']}\")\n", "        print(f\"  Accuracy: {summary['accuracy']:.2f}\")\n", "        print(f\"  Final Prediction: {summary['final_prediction']:.3f}\")\n", "        print(f\"  Improvement: {summary['improvement']:+.3f}\")\n", "else:\n", "    print(\"No models available for real-time system demo.\")\n", "\n", "print(\"\\n✅ Prediction demo completed successfully!\")\n", "print(\"\\n💡 This system can be integrated into:\")\n", "print(\"   - Adaptive learning platforms\")\n", "print(\"   - Intelligent tutoring systems\")\n", "print(\"   - Educational assessment tools\")\n", "print(\"   - Learning analytics dashboards\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}