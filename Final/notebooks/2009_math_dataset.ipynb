{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f0a48d31-8eb5-4911-baf2-84c8a2764d7d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-19 15:40:14.749863: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: SSE4.1 SSE4.2 AVX AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["All imports successful!\n"]}], "source": ["import os\n", "import sys\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "import json\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "# Add pipeline to path\n", "sys.path.append('../pipeline')\n", "\n", "# Import pipeline components\n", "from kt_training_pipeline import KnowledgeTracingPipeline\n", "from kt_evaluation import KTEvaluator\n", "from prediction import KTPredictor\n", "\n", "print(\"All imports successful!\")\n"]}, {"cell_type": "code", "execution_count": 2, "id": "2d7d3782-ab92-4bbe-a216-05c345427b09", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading 2009_math_skills dataset...\n", "Dataset loaded successfully!\n", "Shape: (401756, 30)\n", "Columns: ['order_id', 'assignment_id', 'user_id', 'assistment_id', 'problem_id', 'original', 'correct', 'attempt_count', 'ms_first_response', 'tutor_mode', 'answer_type', 'sequence_id', 'student_class_id', 'position', 'type', 'base_sequence_id', 'skill_id', 'skill_name', 'teacher_id', 'school_id', 'hint_count', 'hint_total', 'overlap_time', 'template_id', 'answer_id', 'answer_text', 'first_action', 'bottom_hint', 'opportunity', 'opportunity_original']\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 401756 entries, 0 to 401755\n", "Data columns (total 30 columns):\n", " #   Column                Non-Null Count   Dtype  \n", "---  ------                --------------   -----  \n", " 0   order_id              401756 non-null  int64  \n", " 1   assignment_id         401756 non-null  int64  \n", " 2   user_id               401756 non-null  int64  \n", " 3   assistment_id         401756 non-null  int64  \n", " 4   problem_id            401756 non-null  int64  \n", " 5   original              401756 non-null  int64  \n", " 6   correct               401756 non-null  int64  \n", " 7   attempt_count         401756 non-null  int64  \n", " 8   ms_first_response     401756 non-null  int64  \n", " 9   tutor_mode            401756 non-null  object \n", " 10  answer_type           401756 non-null  object \n", " 11  sequence_id           401756 non-null  int64  \n", " 12  student_class_id      401756 non-null  int64  \n", " 13  position              401756 non-null  int64  \n", " 14  type                  401756 non-null  object \n", " 15  base_sequence_id      401756 non-null  int64  \n", " 16  skill_id              338001 non-null  float64\n", " 17  skill_name            325637 non-null  object \n", " 18  teacher_id            401756 non-null  int64  \n", " 19  school_id             401756 non-null  int64  \n", " 20  hint_count            401756 non-null  int64  \n", " 21  hint_total            401756 non-null  int64  \n", " 22  overlap_time          401756 non-null  int64  \n", " 23  template_id           401756 non-null  int64  \n", " 24  answer_id             45454 non-null   float64\n", " 25  answer_text           312548 non-null  object \n", " 26  first_action          401756 non-null  int64  \n", " 27  bottom_hint           67044 non-null   float64\n", " 28  opportunity           401756 non-null  int64  \n", " 29  opportunity_original  328291 non-null  float64\n", "dtypes: float64(4), int64(21), object(5)\n", "memory usage: 92.0+ MB\n"]}], "source": ["# Load the math dataset\n", "data_path = \"/home/<USER>/workspace/AClass/App/Training/datasets/math/skill_builder_data_corrected.csv\"\n", "\n", "print(\"Loading 2009_math_skills dataset...\")\n", "df = pd.read_csv(data_path, encoding='latin1')\n", "\n", "print(f\"Dataset loaded successfully!\")\n", "print(f\"Shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")\n", "\n", "# Display basic info\n", "df.info()"]}, {"cell_type": "code", "execution_count": 3, "id": "da33014a", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'DataFrame' object has no attribute 'sort_valuesm'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipykernel_32062/1729338214.py\u001b[0m in \u001b[0;36m?\u001b[0;34m()\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0mpd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mset_option\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'display.max_columns'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m500\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      3\u001b[0m \u001b[0mdf\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msort_valuesm\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'skill_name'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'order_id'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0minplace\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0mdf\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mhead\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;36m100\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/pandas/core/generic.py\u001b[0m in \u001b[0;36m?\u001b[0;34m(self, name)\u001b[0m\n\u001b[1;32m   6295\u001b[0m             \u001b[0;32mand\u001b[0m \u001b[0mname\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_accessors\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   6296\u001b[0m             \u001b[0;32mand\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_info_axis\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_can_hold_identifiers_and_holds_name\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mname\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   6297\u001b[0m         ):\n\u001b[1;32m   6298\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mname\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 6299\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mobject\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__getattribute__\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mname\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m: 'DataFrame' object has no attribute 'sort_valuesm'"]}], "source": ["pd.set_option('display.max_columns', 500)\n", "df.sort_valuesm(['skill_name', 'order_id'], inplace=True)\n", "df.head(100)"]}, {"cell_type": "code", "execution_count": null, "id": "a28882df", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>assignment_id</th>\n", "      <th>user_id</th>\n", "      <th>assistment_id</th>\n", "      <th>problem_id</th>\n", "      <th>original</th>\n", "      <th>correct</th>\n", "      <th>attempt_count</th>\n", "      <th>ms_first_response</th>\n", "      <th>sequence_id</th>\n", "      <th>student_class_id</th>\n", "      <th>position</th>\n", "      <th>base_sequence_id</th>\n", "      <th>skill_id</th>\n", "      <th>teacher_id</th>\n", "      <th>school_id</th>\n", "      <th>hint_count</th>\n", "      <th>hint_total</th>\n", "      <th>overlap_time</th>\n", "      <th>template_id</th>\n", "      <th>answer_id</th>\n", "      <th>first_action</th>\n", "      <th>bottom_hint</th>\n", "      <th>opportunity</th>\n", "      <th>opportunity_original</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>4.017560e+05</td>\n", "      <td>401756.000000</td>\n", "      <td>401756.000000</td>\n", "      <td>401756.000000</td>\n", "      <td>401756.000000</td>\n", "      <td>401756.000000</td>\n", "      <td>401756.000000</td>\n", "      <td>401756.000000</td>\n", "      <td>4.017560e+05</td>\n", "      <td>401756.000000</td>\n", "      <td>401756.000000</td>\n", "      <td>401756.000000</td>\n", "      <td>401756.000000</td>\n", "      <td>338001.000000</td>\n", "      <td>401756.000000</td>\n", "      <td>401756.000000</td>\n", "      <td>401756.000000</td>\n", "      <td>401756.000000</td>\n", "      <td>4.017560e+05</td>\n", "      <td>401756.000000</td>\n", "      <td>45454.000000</td>\n", "      <td>401756.000000</td>\n", "      <td>67044.000000</td>\n", "      <td>401756.000000</td>\n", "      <td>328291.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>3.066256e+07</td>\n", "      <td>273701.845882</td>\n", "      <td>83414.154542</td>\n", "      <td>46443.517526</td>\n", "      <td>81117.030011</td>\n", "      <td>0.817140</td>\n", "      <td>0.642923</td>\n", "      <td>1.596417</td>\n", "      <td>4.748464e+04</td>\n", "      <td>7284.411088</td>\n", "      <td>12919.115222</td>\n", "      <td>57.163649</td>\n", "      <td>6786.020985</td>\n", "      <td>127.167032</td>\n", "      <td>46875.587322</td>\n", "      <td>3031.291025</td>\n", "      <td>0.487470</td>\n", "      <td>2.235817</td>\n", "      <td>5.964848e+04</td>\n", "      <td>39571.335029</td>\n", "      <td>145094.431667</td>\n", "      <td>0.130012</td>\n", "      <td>0.724092</td>\n", "      <td>20.553535</td>\n", "      <td>14.403307</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>5.264886e+06</td>\n", "      <td>11338.460017</td>\n", "      <td>7417.814021</td>\n", "      <td>11832.443427</td>\n", "      <td>25426.799662</td>\n", "      <td>0.386552</td>\n", "      <td>0.479139</td>\n", "      <td>12.050437</td>\n", "      <td>3.614590e+05</td>\n", "      <td>1497.941072</td>\n", "      <td>783.548291</td>\n", "      <td>65.215464</td>\n", "      <td>1263.359735</td>\n", "      <td>120.427518</td>\n", "      <td>15892.975481</td>\n", "      <td>1830.451486</td>\n", "      <td>1.187255</td>\n", "      <td>1.804244</td>\n", "      <td>3.822188e+05</td>\n", "      <td>12679.439926</td>\n", "      <td>47127.478285</td>\n", "      <td>0.394099</td>\n", "      <td>0.446974</td>\n", "      <td>62.523994</td>\n", "      <td>62.393684</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>2.022408e+07</td>\n", "      <td>217900.000000</td>\n", "      <td>14.000000</td>\n", "      <td>86.000000</td>\n", "      <td>83.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>-7.759575e+06</td>\n", "      <td>5870.000000</td>\n", "      <td>11644.000000</td>\n", "      <td>1.000000</td>\n", "      <td>5870.000000</td>\n", "      <td>1.000000</td>\n", "      <td>11158.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>-7.759575e+06</td>\n", "      <td>86.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>2.660218e+07</td>\n", "      <td>266784.000000</td>\n", "      <td>78970.000000</td>\n", "      <td>37046.000000</td>\n", "      <td>58467.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>8.518000e+03</td>\n", "      <td>5979.000000</td>\n", "      <td>12352.000000</td>\n", "      <td>9.000000</td>\n", "      <td>5968.000000</td>\n", "      <td>39.000000</td>\n", "      <td>42999.000000</td>\n", "      <td>2770.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.066900e+04</td>\n", "      <td>30244.000000</td>\n", "      <td>104412.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>3.110513e+07</td>\n", "      <td>271629.000000</td>\n", "      <td>80111.000000</td>\n", "      <td>44498.000000</td>\n", "      <td>80734.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.945300e+04</td>\n", "      <td>6910.000000</td>\n", "      <td>12574.000000</td>\n", "      <td>27.000000</td>\n", "      <td>6094.000000</td>\n", "      <td>74.000000</td>\n", "      <td>45778.000000</td>\n", "      <td>2770.000000</td>\n", "      <td>0.000000</td>\n", "      <td>3.000000</td>\n", "      <td>2.426450e+04</td>\n", "      <td>30987.000000</td>\n", "      <td>136247.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>8.000000</td>\n", "      <td>6.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>3.494364e+07</td>\n", "      <td>279158.000000</td>\n", "      <td>88142.000000</td>\n", "      <td>53142.000000</td>\n", "      <td>93102.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>4.457825e+04</td>\n", "      <td>8032.000000</td>\n", "      <td>13241.000000</td>\n", "      <td>92.000000</td>\n", "      <td>7014.000000</td>\n", "      <td>279.000000</td>\n", "      <td>59882.000000</td>\n", "      <td>5056.000000</td>\n", "      <td>0.000000</td>\n", "      <td>4.000000</td>\n", "      <td>5.698925e+04</td>\n", "      <td>46399.000000</td>\n", "      <td>184077.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>19.000000</td>\n", "      <td>13.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3.831020e+07</td>\n", "      <td>291503.000000</td>\n", "      <td>96299.000000</td>\n", "      <td>106210.000000</td>\n", "      <td>207348.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>3824.000000</td>\n", "      <td>8.407692e+07</td>\n", "      <td>13362.000000</td>\n", "      <td>14415.000000</td>\n", "      <td>295.000000</td>\n", "      <td>13362.000000</td>\n", "      <td>378.000000</td>\n", "      <td>69274.000000</td>\n", "      <td>9948.000000</td>\n", "      <td>10.000000</td>\n", "      <td>10.000000</td>\n", "      <td>8.407692e+07</td>\n", "      <td>106180.000000</td>\n", "      <td>323181.000000</td>\n", "      <td>2.000000</td>\n", "      <td>1.000000</td>\n", "      <td>3371.000000</td>\n", "      <td>3371.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           order_id  assignment_id        user_id  assistment_id  \\\n", "count  4.017560e+05  401756.000000  401756.000000  401756.000000   \n", "mean   3.066256e+07  273701.845882   83414.154542   46443.517526   \n", "std    5.264886e+06   11338.460017    7417.814021   11832.443427   \n", "min    2.022408e+07  217900.000000      14.000000      86.000000   \n", "25%    2.660218e+07  266784.000000   78970.000000   37046.000000   \n", "50%    3.110513e+07  271629.000000   80111.000000   44498.000000   \n", "75%    3.494364e+07  279158.000000   88142.000000   53142.000000   \n", "max    3.831020e+07  291503.000000   96299.000000  106210.000000   \n", "\n", "          problem_id       original        correct  attempt_count  \\\n", "count  401756.000000  401756.000000  401756.000000  401756.000000   \n", "mean    81117.030011       0.817140       0.642923       1.596417   \n", "std     25426.799662       0.386552       0.479139      12.050437   \n", "min        83.000000       0.000000       0.000000       0.000000   \n", "25%     58467.000000       1.000000       0.000000       1.000000   \n", "50%     80734.000000       1.000000       1.000000       1.000000   \n", "75%     93102.000000       1.000000       1.000000       1.000000   \n", "max    207348.000000       1.000000       1.000000    3824.000000   \n", "\n", "       ms_first_response    sequence_id  student_class_id       position  \\\n", "count       4.017560e+05  401756.000000     401756.000000  401756.000000   \n", "mean        4.748464e+04    7284.411088      12919.115222      57.163649   \n", "std         3.614590e+05    1497.941072        783.548291      65.215464   \n", "min        -7.759575e+06    5870.000000      11644.000000       1.000000   \n", "25%         8.518000e+03    5979.000000      12352.000000       9.000000   \n", "50%         1.945300e+04    6910.000000      12574.000000      27.000000   \n", "75%         4.457825e+04    8032.000000      13241.000000      92.000000   \n", "max         8.407692e+07   13362.000000      14415.000000     295.000000   \n", "\n", "       base_sequence_id       skill_id     teacher_id      school_id  \\\n", "count     401756.000000  338001.000000  401756.000000  401756.000000   \n", "mean        6786.020985     127.167032   46875.587322    3031.291025   \n", "std         1263.359735     120.427518   15892.975481    1830.451486   \n", "min         5870.000000       1.000000   11158.000000       1.000000   \n", "25%         5968.000000      39.000000   42999.000000    2770.000000   \n", "50%         6094.000000      74.000000   45778.000000    2770.000000   \n", "75%         7014.000000     279.000000   59882.000000    5056.000000   \n", "max        13362.000000     378.000000   69274.000000    9948.000000   \n", "\n", "          hint_count     hint_total  overlap_time    template_id  \\\n", "count  401756.000000  401756.000000  4.017560e+05  401756.000000   \n", "mean        0.487470       2.235817  5.964848e+04   39571.335029   \n", "std         1.187255       1.804244  3.822188e+05   12679.439926   \n", "min         0.000000       0.000000 -7.759575e+06      86.000000   \n", "25%         0.000000       0.000000  1.066900e+04   30244.000000   \n", "50%         0.000000       3.000000  2.426450e+04   30987.000000   \n", "75%         0.000000       4.000000  5.698925e+04   46399.000000   \n", "max        10.000000      10.000000  8.407692e+07  106180.000000   \n", "\n", "           answer_id   first_action   bottom_hint    opportunity  \\\n", "count   45454.000000  401756.000000  67044.000000  401756.000000   \n", "mean   145094.431667       0.130012      0.724092      20.553535   \n", "std     47127.478285       0.394099      0.446974      62.523994   \n", "min         1.000000       0.000000      0.000000       1.000000   \n", "25%    104412.000000       0.000000      0.000000       3.000000   \n", "50%    136247.000000       0.000000      1.000000       8.000000   \n", "75%    184077.000000       0.000000      1.000000      19.000000   \n", "max    323181.000000       2.000000      1.000000    3371.000000   \n", "\n", "       opportunity_original  \n", "count         328291.000000  \n", "mean              14.403307  \n", "std               62.393684  \n", "min                1.000000  \n", "25%                3.000000  \n", "50%                6.000000  \n", "75%               13.000000  \n", "max             3371.000000  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()\n"]}, {"cell_type": "code", "execution_count": null, "id": "fddd4be9-a7e2-455b-b6f5-2ef56067b6e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values in key columns:\n", "user_id: 0 (0.00%)\n", "problem_id: 0 (0.00%)\n", "skill_name: 76119 (18.95%)\n", "correct: 0 (0.00%)\n"]}], "source": ["# Check for missing values in key columns\n", "key_columns = ['user_id', 'problem_id', 'skill_name', 'correct']\n", "print(\"Missing values in key columns:\")\n", "for col in key_columns:\n", "    if col in df_clean.columns:\n", "        missing = df_clean[col].isnull().sum()\n", "        print(f\"{col}: {missing} ({missing/len(df_clean)*100:.2f}%)\")\n", "    else:\n", "        print(f\"{col}: Column not found\")"]}, {"cell_type": "code", "execution_count": null, "id": "0411ed18-a337-45c7-be25-f085fde33515", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cleaning dataset...\n", "Original size: 401,756\n", "Cleaned size: 325,637\n", "Removed: 76,119 rows (18.95%)\n"]}], "source": ["# Clean the dataset\n", "print(\"Cleaning dataset...\")\n", "original_size = len(df_clean)\n", "\n", "# Remove rows with missing values in essential columns\n", "essential_cols = [col for col in key_columns if col in df_clean.columns]\n", "df_clean = df_clean.dropna(subset=essential_cols)\n", "\n", "# Remove duplicates\n", "df_clean = df_clean.drop_duplicates()\n", "\n", "# Ensure correct data types\n", "if 'correct' in df_clean.columns:\n", "    df_clean['correct'] = df_clean['correct'].astype(int)\n", "if 'user_id' in df_clean.columns:\n", "    df_clean['user_id'] = df_clean['user_id'].astype(str)\n", "if 'problem_id' in df_clean.columns:\n", "    df_clean['problem_id'] = df_clean['problem_id'].astype(str)\n", "\n", "print(f\"Original size: {original_size:,}\")\n", "print(f\"Cleaned size: {len(df_clean):,}\")\n", "print(f\"Removed: {original_size - len(df_clean):,} rows ({(original_size - len(df_clean))/original_size*100:.2f}%)\")"]}, {"cell_type": "code", "execution_count": null, "id": "4b4bb38c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The number of records: 346860\n", "The number of skills: 111\n", "The number of users: 4217\n", "The number of problems: 26688\n", "The number of assistments: 17725\n", "The number of assignments: 3521\n"]}], "source": ["print(\"The number of records: \"+ str(len(df['order_id'].unique())))\n", "print(\"The number of skills: \"+ str(len(df['skill_name'].unique())))\n", "print(\"The number of users: \"+ str(len(df['user_id'].unique())))\n", "print(\"The number of problems: \"+ str(len(df['problem_id'].unique())))\n", "print(\"The number of assistments: \"+ str(len(df['assistment_id'].unique())))\n", "print(\"The number of assignments: \"+ str(len(df['assignment_id'].unique())))\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "c35ba78e-90c1-4e72-95b1-5e396e5331fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📋 COMPREHENSIVE SKILLS ANALYSIS\n", "==================================================\n", "Total number of unique skills: 110\n", "\n", "Top 15 skills by number of interactions:\n", "                                          interactions  accuracy  accuracy_std  unique_students  unique_problems\n", "skill_name                                                                                                      \n", "Equation Solving Two or Fewer Steps              24253     0.679         0.467              961             1040\n", "Conversion of Fraction Decimals Percents         18742     0.637         0.481             1225              488\n", "Addition and Subtraction Integers                12741     0.599         0.490             1226              413\n", "Addition and Subtraction Fractions               11334     0.677         0.468             1353              433\n", "Percent Of                                        9497     0.595         0.491             1115              465\n", "Proportion                                        9054     0.641         0.480              756              485\n", "Ordering Fractions                                8539     0.792         0.406              882              464\n", "Equation Solving More Than Two Steps              8115     0.758         0.428              412              419\n", "Probability of Two Distinct Events                7963     0.490         0.500              452              339\n", "Finding Percents                                  7694     0.538         0.499              771              371\n", "Subtraction Whole Numbers                         7669     0.641         0.480              903              242\n", "Probability of a Single Event                     7438     0.742         0.437              939              350\n", "Pattern Finding                                   7343     0.600         0.490              447              554\n", "Absolute Value                                    7340     0.757         0.429             1002              241\n", "Ordering Positive Decimals                        7317     0.750         0.433              942              543\n", "\n", "Top 15 easiest skills (highest accuracy):\n", "                                              interactions  accuracy  unique_students\n", "skill_name                                                                           \n", "Nets of 3D Figures                                     280     0.950              229\n", "Area Parallelogram                                     115     0.922               95\n", "Congruence                                             587     0.894              364\n", "Distributive Property                                   18     0.889                5\n", "Mode                                                  1926     0.876              572\n", "Scatter Plot                                          1859     0.869              354\n", "Area Rectangle                                         495     0.863              215\n", "Area Triangle                                          286     0.857              168\n", "D.4.8-understanding-concept-of-probabilities           456     0.846              202\n", "Volume Rectangular Prism                               926     0.840              345\n", "Fraction Of                                            607     0.830              288\n", "Write Linear Equation from Situation                  1447     0.822              223\n", "Linear Equations                                        89     0.820               41\n", "Slope                                                   89     0.820               41\n", "Choose an Equation from Given Information               89     0.820               41\n", "\n", "Top 15 hardest skills (lowest accuracy):\n", "                                               interactions  accuracy  unique_students\n", "skill_name                                                                            \n", "Reading a Ruler or Scale                                  5     0.000                5\n", "Quadratic Formula to Solve Quadratic Equation            32     0.125               14\n", "Rotations                                               427     0.136              163\n", "Computation with Real Numbers                            21     0.190               21\n", "Solving Systems of Linear Equations                     234     0.192               22\n", "Percent Discount                                         47     0.234               29\n", "Surface Area Cylinder                                   491     0.316              135\n", "Finding Slope From Situation                              9     0.333                2\n", "Percents                                                117     0.333               41\n", "Algebraic Solving                                       389     0.368               88\n", "Reflection                                              459     0.373              176\n", "Rate                                                     91     0.374               39\n", "Algebraic Simplification                                 90     0.400               15\n", "Finding <PERSON><PERSON><PERSON> from Ordered Pairs                          5     0.400                2\n", "Multiplication Whole Numbers                            110     0.436               45\n"]}], "source": ["# Comprehensive skills analysis\n", "print(\"📋 COMPREHENSIVE SKILLS ANALYSIS\")\n", "print(\"=\"*50)\n", "\n", "# Get all unique skills\n", "all_skills = df_clean['skill_name'].unique()\n", "print(f\"Total number of unique skills: {len(all_skills)}\")\n", "\n", "# Skills statistics\n", "skills_stats = df_clean.groupby('skill_name').agg({\n", "    'correct': ['count', 'mean', 'std'],\n", "    'user_id': 'nunique',\n", "    'problem_id': 'nunique'\n", "}).round(3)\n", "\n", "skills_stats.columns = ['interactions', 'accuracy', 'accuracy_std', 'unique_students', 'unique_problems']\n", "skills_stats = skills_stats.sort_values('interactions', ascending=False)\n", "\n", "print(\"\\nTop 15 skills by number of interactions:\")\n", "print(skills_stats.head(15).to_string())\n", "\n", "print(\"\\nTop 15 easiest skills (highest accuracy):\")\n", "easiest_skills = skills_stats.sort_values('accuracy', ascending=False).head(15)\n", "print(easiest_skills[['interactions', 'accuracy', 'unique_students']].to_string())\n", "\n", "print(\"\\nTop 15 hardest skills (lowest accuracy):\")\n", "hardest_skills = skills_stats.sort_values('accuracy', ascending=True).head(15)\n", "print(hardest_skills[['interactions', 'accuracy', 'unique_students']].to_string())"]}, {"cell_type": "code", "execution_count": null, "id": "134a4efa", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize skill distribution\n", "fig, axes = plt.subplots(3, 2, figsize=(15, 12))\n", "\n", "# 1. Skill frequency distribution\n", "skill_counts = df_clean['skill_name'].value_counts().head(20)\n", "axes[0, 0].barh(range(len(skill_counts)), skill_counts.values)\n", "axes[0, 0].set_yticks(range(len(skill_counts)))\n", "axes[0, 0].set_yticklabels(skill_counts.index, fontsize=8)\n", "axes[0, 0].set_title('Top 20 Skills by Frequency')\n", "axes[0, 0].set_xlabel('Number of Interactions')\n", "\n", "# Top 20 skills by attempts\n", "top_skills = skills_stats.head(20)\n", "axes[0,1].barh(range(len(top_skills)), top_skills['interactions'])\n", "axes[0,1].set_yticks(range(len(top_skills)))\n", "axes[0,1].set_yticklabels(top_skills.index, fontsize=8)\n", "axes[0,1].set_xlabel('Number of Attempts')\n", "axes[0,1].set_title('Top 20 Skills by Attempts')\n", "axes[0,1].invert_yaxis()\n", "\n", "\n", "# User performance distribution\n", "user_accuracy = df_clean.groupby('user_id')['correct'].mean()\n", "axes[1,0].hist(user_accuracy, bins=30, alpha=0.7)\n", "axes[1,0].set_xlabel('User Accuracy')\n", "axes[1,0].set_ylabel('Number of Users')\n", "axes[1,0].set_title('Distribution of User Accuracy')\n", "\n", "# Attempts per user\n", "user_attempts = df_clean.groupby('user_id').size()\n", "axes[1,1].hist(user_attempts, bins=50, alpha=0.7)\n", "axes[1,1].set_xlabel('Number of Attempts')\n", "axes[1,1].set_ylabel('Number of Users')\n", "axes[1,1].set_title('Distribution of Attempts per User')\n", "axes[1,1].set_xlim(0, user_attempts.quantile(0.95))\n", "\n", "# Accuracy distribution\n", "axes[2,0].hist(skills_stats['accuracy'], bins=20, alpha=0.7)\n", "axes[2,0].set_xlabel('Accuracy')\n", "axes[2,0].set_ylabel('Number of Skills')\n", "axes[2,0].set_title('Distribution of Skill Accuracy')\n", "\n", "# 4. Overall accuracy distribution\n", "axes[2,1].hist(df_clean['correct'], bins=2, alpha=0.7, edgecolor='black')\n", "axes[2,1].set_title('Overall Accuracy Distribution')\n", "axes[2,1].set_xlabel('Correct (0=Incorrect, 1=Correct)')\n", "axes[2,1].set_ylabel('Number of Interactions')\n", "axes[2,1].set_xticks([0, 1])\n", "\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "806a7aaa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 SKILLS CATEGORIZATION\n", "==============================\n", "Easy skills (≥80% accuracy): 17 skills\n", "Medium skills (60-80% accuracy): 54 skills\n", "Hard skills (<60% accuracy): 39 skills\n", "\n", "High frequency skills (≥1000 interactions): 63 skills\n", "Medium frequency skills (100-1000 interactions): 29 skills\n", "Low frequency skills (<100 interactions): 18 skills\n", "\n", "💾 Skills analysis saved to App/Training/reports/math/skills_analysis.csv\n"]}], "source": ["# Skills categorization analysis\n", "print(\"\\n📊 SKILLS CATEGORIZATION\")\n", "print(\"=\"*30)\n", "\n", "# Categorize skills by difficulty\n", "easy_skills = skills_stats[skills_stats['accuracy'] >= 0.8]\n", "medium_skills = skills_stats[(skills_stats['accuracy'] >= 0.6) & (skills_stats['accuracy'] < 0.8)]\n", "hard_skills = skills_stats[skills_stats['accuracy'] < 0.6]\n", "\n", "print(f\"Easy skills (≥80% accuracy): {len(easy_skills)} skills\")\n", "print(f\"Medium skills (60-80% accuracy): {len(medium_skills)} skills\")\n", "print(f\"Hard skills (<60% accuracy): {len(hard_skills)} skills\")\n", "\n", "# Categorize by frequency\n", "high_freq = skills_stats[skills_stats['interactions'] >= 1000]\n", "medium_freq = skills_stats[(skills_stats['interactions'] >= 100) & (skills_stats['interactions'] < 1000)]\n", "low_freq = skills_stats[skills_stats['interactions'] < 100]\n", "\n", "print(f\"\\nHigh frequency skills (≥1000 interactions): {len(high_freq)} skills\")\n", "print(f\"Medium frequency skills (100-1000 interactions): {len(medium_freq)} skills\")\n", "print(f\"Low frequency skills (<100 interactions): {len(low_freq)} skills\")\n", "\n", "# Save skills analysis\n", "os.makedirs('App/Training/reports/math', exist_ok=True)\n", "skills_stats.to_csv('App/Training/reports/math/skills_analysis.csv')\n", "print(\"\\n💾 Skills analysis saved to App/Training/reports/math/skills_analysis.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "267baa18", "metadata": {}, "outputs": [], "source": ["# #export csv list of all unique skills from df\n", "# skills = df_clean['skill_name'].unique()\n", "# skills_df = pd.DataFrame(skills, columns=['skill_name'])\n", "# skills_df.to_csv('skills_all.csv', index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}